# Professional FPS Controller

A proper first-person shooter controller based on Unity's Starter Assets and professional game development practices.

## What This Actually Is

This is a **proper** FPS controller that follows the same patterns used by:
- Unity's Starter Assets First Person Controller
- NeoFPS (industry standard)
- Professional shipped games

**No more overcomplicated bullshit.** This uses the proven approach that actually works.

## Key Features

### ✅ **Uses CharacterController** (Not Rigidbody)
- This is how **every** professional FPS game does it
- Predictable, stable movement
- No physics glitches or weird behavior
- Proper collision detection

### ✅ **Clean Architecture**
- `FirstPersonController.cs` - Main movement and camera logic
- `ProceduralAnimations.cs` - All the fancy animation effects
- `FPSSetup.cs` - Easy setup script

### ✅ **PurrNet Integration**
- Uses `SyncInput<T>` for networked input
- Server-authoritative movement
- Client prediction for responsive feel

### ✅ **Procedural Animations**
- Head bobbing during movement
- Weapon sway based on mouse input
- Breathing animation when idle
- Camera shake effects
- Recoil animations

## PurrNet Player Prefab Setup

### 1. Create Player Prefab

1. **Create an empty GameObject** named "Player"
2. **Add the `PlayerPrefabSetup` component**
3. **Click "Setup Player Prefab" in the context menu** (right-click component)
4. **Save as a prefab** in your Prefabs folder
5. **Remove the `PlayerPrefabSetup` component** (it's only for setup)

### 2. Configure PurrNet

1. **Add your player prefab** to the NetworkPrefabs ScriptableObject
2. **Set it as the player prefab** in your NetworkManager
3. **Configure spawn settings** in your game's networking code

### 3. Test

Hit play and PurrNet will spawn the player with full FPS functionality!

## How It Actually Works

### Movement System
```csharp
// Uses CharacterController.Move() - the professional way
_controller.Move(targetDirection.normalized * (_speed * Time.deltaTime) +
                 new Vector3(0.0f, _verticalVelocity, 0.0f) * Time.deltaTime);
```

### Camera Control
```csharp
// Proper camera rotation with clamping
_cinemachineTargetYaw += _look.x * deltaTimeMultiplier;
_cinemachineTargetPitch += _look.y * deltaTimeMultiplier;
```

### Networking
```csharp
// Clean input synchronization
[SerializeField] private SyncInput<Vector2> _moveInput = new();
[SerializeField] private SyncInput<Vector2> _lookInput = new();
```

## Settings You Can Tweak

### Movement
- **Move Speed**: Base walking speed
- **Sprint Speed**: Running speed
- **Jump Height**: How high you jump
- **Gravity**: Custom gravity value

### Camera
- **Top/Bottom Clamp**: Vertical look limits
- **Mouse Sensitivity**: Adjust in Input System settings

### Procedural Animations
- **Head Bob**: Frequency and amplitude
- **Weapon Sway**: Amount and smoothing
- **Breathing**: Rate and intensity
- **Camera Shake**: Decay rate
- **Recoil**: Recovery speed

## Testing Effects

Call effects from your game code:
```csharp
// Get the animation component
ProceduralAnimations anim = GetComponent<ProceduralAnimations>();

// Add camera shake (e.g., when taking damage)
anim.AddCameraShake(0.5f);

// Add recoil (e.g., when shooting)
anim.AddRecoil(new Vector3(0, 0, -0.1f), new Vector3(-2f, 0, 0));
```

## Player Prefab Structure

After setup, your player prefab will have:
```
Player (Root)
├── CharacterController
├── NetworkIdentity (PurrNet)
├── NetworkTransform (PurrNet)
├── PlayerInput (Unity Input System)
├── FirstPersonController (Movement & Camera)
├── ProceduralAnimations (Effects)
└── CameraTarget
    └── PlayerCamera
        ├── Camera
        └── AudioListener
```

## Why This Is Better

### ❌ **What I Did Wrong Before:**
- Overcomplicated multi-script architecture
- Used Rigidbody (causes physics issues)
- Reinvented the wheel poorly
- Split everything into unnecessary components

### ✅ **What This Does Right:**
- Follows Unity's proven patterns
- Uses CharacterController (industry standard)
- Clean, maintainable code
- Actually works reliably

## Input System Setup

Make sure your Input Actions include:
- **Move** (Vector2) - WASD
- **Look** (Vector2) - Mouse Delta
- **Jump** (Button) - Space
- **Sprint** (Button) - Left Shift

The controller will automatically use these if they exist in your Input System Actions.

## PurrNet Integration Details

### Automatic Ownership Handling
- Only the **owner** processes input and controls camera
- Non-owners automatically disable PlayerInput component
- Camera is disabled for non-owners

### Server Authority
- **Server** handles all movement validation via CharacterController
- **Clients** receive position updates via NetworkTransform
- Input is sent via SyncInput and automatically filtered

### Network Performance
- SyncInput only sends changed values
- CharacterController movement is deterministic
- Efficient bandwidth usage

## Performance

- Frame-rate independent (uses Time.deltaTime properly)
- Efficient input handling
- No unnecessary calculations
- Optimized for multiplayer

## Troubleshooting

### "Camera not moving"
- Check that the player has network ownership
- Verify Input System is set up correctly

### "Jittery movement"
- This shouldn't happen with CharacterController
- If it does, check your network setup

### "Input not working"
- Verify Input System Actions are configured
- Check that PlayerInput component is enabled

## The Bottom Line

This is how you **actually** make an FPS controller in Unity. No more reinventing the wheel. Use the proven approach that works.

**Stop overthinking it. This just works.**
