using UnityEngine;
using PurrNet;

namespace FPS
{
    /// <summary>
    /// Main FPS Controller that coordinates all FPS components
    /// This is the main script to attach to your FPS player GameObject
    /// </summary>
    [RequireComponent(typeof(FPSInputHandler))]
    [RequireComponent(typeof(FPSMovementController))]
    [RequireComponent(typeof(FPSCameraController))]
    [RequireComponent(typeof(ProceduralAnimationSystem))]
    public class FPSController : NetworkBehaviour
    {
        [Header("FPS Controller Settings")]
        [SerializeField] private bool enableOnStart = true;
        [SerializeField] private bool debugMode = false;
        
        [Header("Component References")]
        [SerializeField] private FPSInputHandler inputHandler;
        [SerializeField] private FPSMovementController movementController;
        [SerializeField] private FPSCameraController cameraController;
        [SerializeField] private ProceduralAnimationSystem animationSystem;
        
        // Events
        public System.Action<FPSController> OnControllerInitialized;
        public System.Action<FPSController> OnControllerDestroyed;
        
        protected override void OnSpawned(bool asServer)
        {
            base.OnSpawned(asServer);
            
            // Get all required components
            GetComponents();
            
            // Validate components
            if (!ValidateComponents())
            {
                Debug.LogError("FPSController: Missing required components!");
                return;
            }
            
            // Initialize controller
            if (enableOnStart)
            {
                InitializeController();
            }
            
            // Trigger initialization event
            OnControllerInitialized?.Invoke(this);
            
            if (debugMode)
            {
                Debug.Log($"FPSController initialized for {(isOwner ? "Owner" : "Non-Owner")} on {(isServer ? "Server" : "Client")}");
            }
        }
        
        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            // Trigger destruction event
            OnControllerDestroyed?.Invoke(this);
            
            if (debugMode)
            {
                Debug.Log("FPSController destroyed");
            }
        }
        
        private void GetComponents()
        {
            // Get components if not assigned
            if (inputHandler == null)
                inputHandler = GetComponent<FPSInputHandler>();
                
            if (movementController == null)
                movementController = GetComponent<FPSMovementController>();
                
            if (cameraController == null)
                cameraController = GetComponent<FPSCameraController>();
                
            if (animationSystem == null)
                animationSystem = GetComponent<ProceduralAnimationSystem>();
        }
        
        private bool ValidateComponents()
        {
            return inputHandler != null && 
                   movementController != null && 
                   cameraController != null && 
                   animationSystem != null;
        }
        
        private void InitializeController()
        {
            // Additional initialization logic can go here
            // Components initialize themselves in their OnSpawned methods
            
            if (debugMode)
            {
                LogControllerState();
            }
        }
        
        private void LogControllerState()
        {
            Debug.Log($"FPS Controller State:" +
                     $"\n- Input Handler: {(inputHandler != null ? "OK" : "MISSING")}" +
                     $"\n- Movement Controller: {(movementController != null ? "OK" : "MISSING")}" +
                     $"\n- Camera Controller: {(cameraController != null ? "OK" : "MISSING")}" +
                     $"\n- Animation System: {(animationSystem != null ? "OK" : "MISSING")}" +
                     $"\n- Is Owner: {isOwner}" +
                     $"\n- Is Server: {isServer}");
        }
        
        #region Public Methods
        
        /// <summary>
        /// Enable or disable the FPS controller
        /// </summary>
        public void SetControllerEnabled(bool enabled)
        {
            if (inputHandler != null)
                inputHandler.enabled = enabled;
                
            if (movementController != null)
                movementController.enabled = enabled;
                
            if (cameraController != null)
                cameraController.enabled = enabled;
                
            if (animationSystem != null)
                animationSystem.enabled = enabled;
        }
        
        /// <summary>
        /// Set mouse sensitivity for the controller
        /// </summary>
        public void SetMouseSensitivity(float sensitivity)
        {
            if (inputHandler != null)
                inputHandler.MouseSensitivity = sensitivity;
                
            if (cameraController != null)
                cameraController.SetMouseSensitivity(sensitivity);
        }
        
        /// <summary>
        /// Set Y-axis inversion for the controller
        /// </summary>
        public void SetInvertYAxis(bool invert)
        {
            if (inputHandler != null)
                inputHandler.InvertYAxis = invert;
                
            if (cameraController != null)
                cameraController.SetInvertYAxis(invert);
        }
        
        /// <summary>
        /// Add camera shake effect
        /// </summary>
        public void AddCameraShake(float intensity)
        {
            if (animationSystem != null)
                animationSystem.AddCameraShake(intensity);
        }
        
        /// <summary>
        /// Add recoil effect
        /// </summary>
        public void AddRecoil(Vector3 positionRecoil, Vector3 rotationRecoil)
        {
            if (animationSystem != null)
                animationSystem.AddRecoil(positionRecoil, rotationRecoil);
        }
        
        /// <summary>
        /// Reset camera to original position
        /// </summary>
        public void ResetCamera()
        {
            if (cameraController != null)
                cameraController.ResetCamera();
        }
        
        #endregion
        
        #region Public Properties
        
        public FPSInputHandler InputHandler => inputHandler;
        public FPSMovementController MovementController => movementController;
        public FPSCameraController CameraController => cameraController;
        public ProceduralAnimationSystem AnimationSystem => animationSystem;
        
        public bool IsGrounded => movementController?.IsGrounded ?? false;
        public bool IsCrouching => movementController?.IsCrouching ?? false;
        public bool IsRunning => movementController?.IsRunning ?? false;
        public Vector3 Velocity => movementController?.Velocity ?? Vector3.zero;
        public Camera PlayerCamera => cameraController?.PlayerCamera;
        
        #endregion
        
        #region Debug
        
        private void OnGUI()
        {
            if (!debugMode || !isOwner)
                return;
                
            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.Label("FPS Controller Debug", GUI.skin.box);
            
            if (movementController != null)
            {
                GUILayout.Label($"Grounded: {movementController.IsGrounded}");
                GUILayout.Label($"Crouching: {movementController.IsCrouching}");
                GUILayout.Label($"Running: {movementController.IsRunning}");
                GUILayout.Label($"Velocity: {movementController.Velocity:F2}");
                GUILayout.Label($"Speed: {movementController.CurrentSpeed:F2}");
            }
            
            if (cameraController != null)
            {
                GUILayout.Label($"Vertical Rotation: {cameraController.VerticalRotation:F1}°");
                GUILayout.Label($"Horizontal Rotation: {cameraController.HorizontalRotation:F1}°");
            }
            
            GUILayout.EndArea();
        }
        
        #endregion
    }
}
