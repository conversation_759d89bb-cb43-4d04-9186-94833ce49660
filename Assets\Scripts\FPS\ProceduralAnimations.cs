using UnityEngine;
using UnityEngine.InputSystem;

namespace FPS
{
    /// <summary>
    /// Professional procedural animation system for FPS
    /// Based on how NeoFPS and other professional controllers handle animations
    /// </summary>
    public class ProceduralAnimations : MonoBehaviour
    {
        [Header("Head Bob")]
        [SerializeField] private bool enableHeadBob = true;
        [SerializeField] private float bobFrequency = 2.0f;
        [SerializeField] private float bobAmplitude = 0.05f;
        [SerializeField] private float bobSmooth = 10f;
        
        [Header("Weapon Sway")]
        [SerializeField] private bool enableWeaponSway = true;
        [SerializeField] private float swayAmount = 0.02f;
        [SerializeField] private float maxSwayAmount = 0.06f;
        [SerializeField] private float swaySmooth = 6f;
        
        [Header("Breathing")]
        [SerializeField] private bool enableBreathing = true;
        [SerializeField] private float breathingRate = 0.5f;
        [SerializeField] private float breathingAmplitude = 0.01f;
        
        [Header("Camera Shake")]
        [SerializeField] private bool enableCameraShake = true;
        [SerializeField] private float shakeDecay = 5f;
        
        [Head<PERSON>("Recoil")]
        [SerializeField] private bool enableRecoil = true;
        [SerializeField] private float recoilRecovery = 4f;

        // References
        private FirstPersonController _controller;
        private CharacterController _characterController;
        private Transform _cameraTransform;
        private Vector3 _originalCameraPosition;
        
        // Head bob
        private float _bobTimer;
        private Vector3 _bobOffset;
        
        // Weapon sway
        private Vector2 _swayOffset;
        private Vector2 _lastMouseInput;
        
        // Breathing
        private float _breathTimer;
        private Vector3 _breathOffset;
        
        // Camera shake
        private Vector3 _shakeOffset;
        private float _shakeIntensity;
        
        // Recoil
        private Vector3 _recoilOffset;
        private Vector3 _recoilRotation;
        
        // Input
        private InputSystem_Actions _input;

        private void Start()
        {
            // Get references
            _controller = GetComponent<FirstPersonController>();
            _characterController = GetComponent<CharacterController>();
            _cameraTransform = Camera.main?.transform;
            
            if (_cameraTransform == null)
            {
                _cameraTransform = FindObjectOfType<Camera>()?.transform;
            }
            
            if (_cameraTransform != null)
            {
                _originalCameraPosition = _cameraTransform.localPosition;
            }
            
            // Setup input
            _input = new InputSystem_Actions();
            _input.Player.Enable();
        }

        private void OnDestroy()
        {
            if (_input != null)
            {
                _input.Player.Disable();
                _input.Dispose();
            }
        }

        private void Update()
        {
            if (_cameraTransform == null) return;

            // Only run animations for the owner
            if (_controller != null && !_controller.isOwner) return;

            // Get input
            Vector2 mouseInput = _input.Player.Look.ReadValue<Vector2>();

            // Update animations
            UpdateHeadBob();
            UpdateWeaponSway(mouseInput);
            UpdateBreathing();
            UpdateCameraShake();
            UpdateRecoil();

            // Apply all offsets
            ApplyAnimations();
        }

        private void UpdateHeadBob()
        {
            if (!enableHeadBob || _characterController == null || _controller == null) return;

            // Get movement speed
            Vector3 velocity = _characterController.velocity;
            float speed = new Vector2(velocity.x, velocity.z).magnitude;

            // Reduce head bob when crouching
            float crouchMultiplier = _controller.IsCrouching ? 0.5f : 1f;

            if (speed > 0.1f && _controller.IsGrounded)
            {
                // Calculate bob with speed-based frequency
                float speedNormalized = Mathf.Clamp01(speed / 5f); // Normalize to typical walking speed
                _bobTimer += Time.deltaTime * bobFrequency * speedNormalized;

                float bobX = Mathf.Sin(_bobTimer) * bobAmplitude * crouchMultiplier;
                float bobY = Mathf.Sin(_bobTimer * 2f) * bobAmplitude * 0.5f * crouchMultiplier;

                Vector3 targetBob = new Vector3(bobX, bobY, 0);
                _bobOffset = Vector3.Lerp(_bobOffset, targetBob, bobSmooth * Time.deltaTime);
            }
            else
            {
                _bobOffset = Vector3.Lerp(_bobOffset, Vector3.zero, bobSmooth * Time.deltaTime);
            }
        }

        private void UpdateWeaponSway(Vector2 mouseInput)
        {
            if (!enableWeaponSway) return;
            
            // Calculate sway based on mouse movement
            Vector2 targetSway = new Vector2(
                -mouseInput.x * swayAmount,
                -mouseInput.y * swayAmount
            );
            
            // Clamp sway
            targetSway.x = Mathf.Clamp(targetSway.x, -maxSwayAmount, maxSwayAmount);
            targetSway.y = Mathf.Clamp(targetSway.y, -maxSwayAmount, maxSwayAmount);
            
            // Smooth sway
            _swayOffset = Vector2.Lerp(_swayOffset, targetSway, swaySmooth * Time.deltaTime);
            
            // Return to center when not moving mouse
            if (mouseInput.magnitude < 0.01f)
            {
                _swayOffset = Vector2.Lerp(_swayOffset, Vector2.zero, swaySmooth * 0.5f * Time.deltaTime);
            }
        }

        private void UpdateBreathing()
        {
            if (!enableBreathing || _controller == null) return;

            // Only breathe when not moving and not sprinting
            Vector3 velocity = _characterController?.velocity ?? Vector3.zero;
            float speed = new Vector2(velocity.x, velocity.z).magnitude;

            if (speed < 0.1f && !_controller.IsSprinting)
            {
                _breathTimer += Time.deltaTime * breathingRate;
                float breathY = Mathf.Sin(_breathTimer) * breathingAmplitude;

                // Reduce breathing when crouching
                if (_controller.IsCrouching)
                {
                    breathY *= 0.7f;
                }

                _breathOffset = Vector3.Lerp(_breathOffset, new Vector3(0, breathY, 0), Time.deltaTime * 2f);
            }
            else
            {
                _breathOffset = Vector3.Lerp(_breathOffset, Vector3.zero, Time.deltaTime * 4f);
            }
        }

        private void UpdateCameraShake()
        {
            if (!enableCameraShake) return;
            
            if (_shakeIntensity > 0f)
            {
                // Generate random shake
                _shakeOffset = new Vector3(
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f)
                ) * _shakeIntensity;
                
                // Decay shake
                _shakeIntensity = Mathf.Lerp(_shakeIntensity, 0f, shakeDecay * Time.deltaTime);
            }
            else
            {
                _shakeOffset = Vector3.zero;
            }
        }

        private void UpdateRecoil()
        {
            if (!enableRecoil) return;
            
            // Smoothly return recoil to zero
            _recoilOffset = Vector3.Lerp(_recoilOffset, Vector3.zero, recoilRecovery * Time.deltaTime);
            _recoilRotation = Vector3.Lerp(_recoilRotation, Vector3.zero, recoilRecovery * Time.deltaTime);
        }

        private void ApplyAnimations()
        {
            // Combine all position offsets
            Vector3 totalOffset = _originalCameraPosition + _bobOffset + _breathOffset + _shakeOffset + _recoilOffset;
            totalOffset += new Vector3(_swayOffset.x, _swayOffset.y, 0);
            
            // Apply position
            _cameraTransform.localPosition = totalOffset;
            
            // Apply rotation (recoil only for now)
            if (_recoilRotation.magnitude > 0.001f)
            {
                _cameraTransform.localRotation = Quaternion.Euler(_recoilRotation);
            }
        }

        #region Public Methods

        /// <summary>
        /// Add camera shake effect
        /// </summary>
        public void AddCameraShake(float intensity)
        {
            _shakeIntensity = Mathf.Max(_shakeIntensity, intensity);
        }

        /// <summary>
        /// Add recoil effect
        /// </summary>
        public void AddRecoil(Vector3 positionRecoil, Vector3 rotationRecoil)
        {
            _recoilOffset += positionRecoil;
            _recoilRotation += rotationRecoil;
        }

        /// <summary>
        /// Reset all animations to default state
        /// </summary>
        public void ResetAnimations()
        {
            _bobOffset = Vector3.zero;
            _swayOffset = Vector2.zero;
            _breathOffset = Vector3.zero;
            _shakeOffset = Vector3.zero;
            _recoilOffset = Vector3.zero;
            _recoilRotation = Vector3.zero;
            _shakeIntensity = 0f;
            
            if (_cameraTransform != null)
            {
                _cameraTransform.localPosition = _originalCameraPosition;
                _cameraTransform.localRotation = Quaternion.identity;
            }
        }

        #endregion

        #region Properties

        public bool HeadBobEnabled
        {
            get => enableHeadBob;
            set => enableHeadBob = value;
        }

        public bool WeaponSwayEnabled
        {
            get => enableWeaponSway;
            set => enableWeaponSway = value;
        }

        public bool BreathingEnabled
        {
            get => enableBreathing;
            set => enableBreathing = value;
        }

        public bool CameraShakeEnabled
        {
            get => enableCameraShake;
            set => enableCameraShake = value;
        }

        public bool RecoilEnabled
        {
            get => enableRecoil;
            set => enableRecoil = value;
        }

        #endregion
    }
}
