using UnityEngine;
using UnityEngine.InputSystem;
using PurrNet;

namespace FPS
{
    /// <summary>
    /// Editor helper to create a proper FPS player prefab for PurrNet
    /// Use this in the editor to set up your player prefab correctly
    /// </summary>
    public class PlayerPrefabSetup : MonoBehaviour
    {
        [Header("Prefab Setup")]
        [SerializeField] private bool setupAsPlayerPrefab = false;
        
        [Head<PERSON>("Camera Settings")]
        [SerializeField] private float cameraHeight = 1.6f;
        [SerializeField] private float fieldOfView = 75f;
        
        [Header("Character Controller Settings")]
        [SerializeField] private float controllerRadius = 0.5f;
        [SerializeField] private float controllerHeight = 2f;
        [SerializeField] private float stepOffset = 0.3f;
        [SerializeField] private float slopeLimit = 45f;

        private void Start()
        {
            // Only run setup in editor or if explicitly requested
            if (setupAsPlayerPrefab && Application.isEditor)
            {
                SetupPlayerPrefab();
            }
            
            // Remove this component after setup (it's only for editor use)
            if (Application.isPlaying)
            {
                Destroy(this);
            }
        }

        [ContextMenu("Setup Player Prefab")]
        public void SetupPlayerPrefab()
        {
            Debug.Log("Setting up FPS Player Prefab...");
            
            // Ensure we have the required components
            SetupCharacterController();
            SetupNetworking();
            SetupInput();
            SetupFPSController();
            SetupCameraHierarchy();
            SetupAnimations();
            
            Debug.Log("FPS Player Prefab setup complete! Remember to:");
            Debug.Log("1. Save this as a prefab");
            Debug.Log("2. Add the prefab to your PurrNet NetworkPrefabs ScriptableObject");
            Debug.Log("3. Configure the NetworkManager to spawn this prefab");
        }

        private void SetupCharacterController()
        {
            CharacterController controller = GetComponent<CharacterController>();
            if (controller == null)
            {
                controller = gameObject.AddComponent<CharacterController>();
            }
            
            controller.center = new Vector3(0, controllerHeight * 0.5f, 0);
            controller.radius = controllerRadius;
            controller.height = controllerHeight;
            controller.stepOffset = stepOffset;
            controller.slopeLimit = slopeLimit;
            
            Debug.Log("✓ CharacterController configured");
        }

        private void SetupNetworking()
        {
            // Add NetworkIdentity if not present
            NetworkIdentity networkIdentity = GetComponent<NetworkIdentity>();
            if (networkIdentity == null)
            {
                networkIdentity = gameObject.AddComponent<NetworkIdentity>();
            }
            
            // Add NetworkTransform for position synchronization
            NetworkTransform networkTransform = GetComponent<NetworkTransform>();
            if (networkTransform == null)
            {
                networkTransform = gameObject.AddComponent<NetworkTransform>();
            }
            
            Debug.Log("✓ PurrNet components added");
        }

        private void SetupInput()
        {
            // Add PlayerInput component
            PlayerInput playerInput = GetComponent<PlayerInput>();
            if (playerInput == null)
            {
                playerInput = gameObject.AddComponent<PlayerInput>();
            }
            
            // Try to find and assign the Input Actions asset
            InputSystem_Actions inputActions = Resources.Load<InputSystem_Actions>("InputSystem_Actions");
            if (inputActions != null)
            {
                playerInput.actions = inputActions.asset;
            }
            else
            {
                Debug.LogWarning("Could not find InputSystem_Actions in Resources. You'll need to assign it manually.");
            }
            
            Debug.Log("✓ PlayerInput configured");
        }

        private void SetupFPSController()
        {
            // Add FirstPersonController
            FirstPersonController fpsController = GetComponent<FirstPersonController>();
            if (fpsController == null)
            {
                fpsController = gameObject.AddComponent<FirstPersonController>();
            }
            
            Debug.Log("✓ FirstPersonController added");
        }

        private void SetupCameraHierarchy()
        {
            // Find or create camera target
            Transform cameraTarget = transform.Find("CameraTarget");
            if (cameraTarget == null)
            {
                GameObject cameraTargetObj = new GameObject("CameraTarget");
                cameraTargetObj.transform.SetParent(transform);
                cameraTargetObj.transform.localPosition = new Vector3(0, cameraHeight, 0);
                cameraTargetObj.transform.localRotation = Quaternion.identity;
                cameraTarget = cameraTargetObj.transform;
            }

            // Find or create player camera
            Transform playerCamera = cameraTarget.Find("PlayerCamera");
            if (playerCamera == null)
            {
                GameObject cameraObj = new GameObject("PlayerCamera");
                cameraObj.transform.SetParent(cameraTarget);
                cameraObj.transform.localPosition = Vector3.zero;
                cameraObj.transform.localRotation = Quaternion.identity;
                cameraObj.tag = "MainCamera";
                
                // Add Camera component
                Camera camera = cameraObj.AddComponent<Camera>();
                camera.fieldOfView = fieldOfView;
                camera.nearClipPlane = 0.1f;
                camera.farClipPlane = 1000f;
                
                // Add AudioListener
                cameraObj.AddComponent<AudioListener>();
                
                playerCamera = cameraObj.transform;
            }

            // Configure the FPS controller's camera target
            FirstPersonController fpsController = GetComponent<FirstPersonController>();
            if (fpsController != null)
            {
                fpsController.CinemachineCameraTarget = cameraTarget.gameObject;
            }
            
            Debug.Log("✓ Camera hierarchy created");
        }

        private void SetupAnimations()
        {
            // Add ProceduralAnimations
            ProceduralAnimations animations = GetComponent<ProceduralAnimations>();
            if (animations == null)
            {
                animations = gameObject.AddComponent<ProceduralAnimations>();
            }
            
            Debug.Log("✓ ProceduralAnimations added");
        }

        private void OnValidate()
        {
            // Update settings when values change in inspector
            if (Application.isPlaying) return;
            
            CharacterController controller = GetComponent<CharacterController>();
            if (controller != null)
            {
                controller.center = new Vector3(0, controllerHeight * 0.5f, 0);
                controller.radius = controllerRadius;
                controller.height = controllerHeight;
                controller.stepOffset = stepOffset;
                controller.slopeLimit = slopeLimit;
            }
            
            Transform cameraTarget = transform.Find("CameraTarget");
            if (cameraTarget != null)
            {
                cameraTarget.localPosition = new Vector3(0, cameraHeight, 0);
            }
            
            Transform playerCamera = cameraTarget?.Find("PlayerCamera");
            if (playerCamera != null)
            {
                Camera camera = playerCamera.GetComponent<Camera>();
                if (camera != null)
                {
                    camera.fieldOfView = fieldOfView;
                }
            }
        }
    }
}
