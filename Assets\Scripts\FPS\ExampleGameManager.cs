using UnityEngine;
using PurrNet;

namespace FPS
{
    /// <summary>
    /// Example of how to integrate the FPS controller with your PurrNet game
    /// This shows the typical patterns for spawning and managing FPS players
    /// </summary>
    public class ExampleGameManager : NetworkBehaviour
    {
        [Header("Player Management")]
        [SerializeField] private GameObject playerPrefab;
        [SerializeField] private Transform[] spawnPoints;
        [SerializeField] private LayerMask playerLayer = 1;
        
        [<PERSON><PERSON>("Game Settings")]
        [SerializeField] private bool autoSpawnOnConnect = true;
        [SerializeField] private float respawnDelay = 3f;
        
        // Events
        public System.Action<FirstPersonController> OnPlayerSpawned;
        public System.Action<FirstPersonController> OnPlayerDespawned;

        protected override void OnSpawned(bool asServer)
        {
            base.OnSpawned(asServer);
            
            // Only server manages player spawning
            if (!isServer)
                return;
                
            // Subscribe to player connection events
            // Note: Replace with your actual PurrNet connection events
            // NetworkManager.OnPlayerConnected += HandlePlayerConnected;
            // NetworkManager.OnPlayerDisconnected += HandlePlayerDisconnected;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            // Unsubscribe from events
            // NetworkManager.OnPlayerConnected -= HandlePlayerConnected;
            // NetworkManager.OnPlayerDisconnected -= HandlePlayerDisconnected;
        }

        /// <summary>
        /// Spawn a player for the given client
        /// Call this from your connection handling code
        /// </summary>
        public void SpawnPlayer(ulong clientId)
        {
            if (!isServer)
                return;
                
            // Find a spawn point
            Vector3 spawnPosition = GetSpawnPosition();
            Quaternion spawnRotation = Quaternion.identity;
            
            // Spawn the player prefab
            GameObject playerObj = Instantiate(playerPrefab, spawnPosition, spawnRotation);
            
            // Get the network identity and assign ownership
            NetworkIdentity networkIdentity = playerObj.GetComponent<NetworkIdentity>();
            if (networkIdentity != null)
            {
                // Assign ownership to the client
                // Note: Replace with actual PurrNet ownership assignment
                // networkIdentity.AssignOwnership(clientId);
            }
            
            // Get the FPS controller
            FirstPersonController fpsController = playerObj.GetComponent<FirstPersonController>();
            if (fpsController != null)
            {
                // Configure any game-specific settings
                ConfigurePlayer(fpsController, clientId);
                
                // Trigger event
                OnPlayerSpawned?.Invoke(fpsController);
            }
            
            Debug.Log($"Player spawned for client {clientId} at {spawnPosition}");
        }

        /// <summary>
        /// Despawn a player
        /// </summary>
        public void DespawnPlayer(ulong clientId)
        {
            if (!isServer)
                return;
                
            // Find the player object for this client
            FirstPersonController[] players = FindObjectsOfType<FirstPersonController>();
            
            foreach (var player in players)
            {
                NetworkIdentity networkIdentity = player.GetComponent<NetworkIdentity>();
                if (networkIdentity != null /* && networkIdentity.OwnerClientId == clientId */)
                {
                    // Trigger event before destroying
                    OnPlayerDespawned?.Invoke(player);
                    
                    // Destroy the player object
                    Destroy(player.gameObject);
                    
                    Debug.Log($"Player despawned for client {clientId}");
                    break;
                }
            }
        }

        /// <summary>
        /// Respawn a player after death
        /// </summary>
        public void RespawnPlayer(ulong clientId)
        {
            if (!isServer)
                return;
                
            // Despawn current player
            DespawnPlayer(clientId);
            
            // Respawn after delay
            Invoke(nameof(DelayedRespawn), respawnDelay);
            
            void DelayedRespawn()
            {
                SpawnPlayer(clientId);
            }
        }

        private Vector3 GetSpawnPosition()
        {
            if (spawnPoints == null || spawnPoints.Length == 0)
            {
                return Vector3.up * 2f; // Default spawn position
            }
            
            // Find a free spawn point
            foreach (Transform spawnPoint in spawnPoints)
            {
                if (IsSpawnPointFree(spawnPoint.position))
                {
                    return spawnPoint.position;
                }
            }
            
            // If no free spawn points, use a random one
            int randomIndex = Random.Range(0, spawnPoints.Length);
            return spawnPoints[randomIndex].position;
        }

        private bool IsSpawnPointFree(Vector3 position)
        {
            // Check if there's already a player at this spawn point
            Collider[] colliders = Physics.OverlapSphere(position, 1f, playerLayer);
            return colliders.Length == 0;
        }

        private void ConfigurePlayer(FirstPersonController player, ulong clientId)
        {
            // Example: Configure player settings based on client
            // You might load player preferences, set team colors, etc.
            
            // Example: Set mouse sensitivity from player preferences
            // float mouseSensitivity = GetPlayerPreference(clientId, "MouseSensitivity", 2f);
            // player.MouseSensitivity = mouseSensitivity;
            
            // Example: Configure player name
            // string playerName = GetPlayerName(clientId);
            // player.gameObject.name = $"Player_{playerName}";
        }

        #region Example Event Handlers
        
        private void HandlePlayerConnected(ulong clientId)
        {
            if (autoSpawnOnConnect)
            {
                SpawnPlayer(clientId);
            }
        }
        
        private void HandlePlayerDisconnected(ulong clientId)
        {
            DespawnPlayer(clientId);
        }
        
        #endregion

        #region Public API for Game Logic
        
        /// <summary>
        /// Get all active players
        /// </summary>
        public FirstPersonController[] GetAllPlayers()
        {
            return FindObjectsOfType<FirstPersonController>();
        }
        
        /// <summary>
        /// Get player by client ID
        /// </summary>
        public FirstPersonController GetPlayer(ulong clientId)
        {
            FirstPersonController[] players = GetAllPlayers();
            
            foreach (var player in players)
            {
                NetworkIdentity networkIdentity = player.GetComponent<NetworkIdentity>();
                if (networkIdentity != null /* && networkIdentity.OwnerClientId == clientId */)
                {
                    return player;
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// Add camera shake to all players (e.g., explosion effect)
        /// </summary>
        public void AddGlobalCameraShake(float intensity)
        {
            FirstPersonController[] players = GetAllPlayers();
            
            foreach (var player in players)
            {
                ProceduralAnimations animations = player.GetComponent<ProceduralAnimations>();
                if (animations != null)
                {
                    animations.AddCameraShake(intensity);
                }
            }
        }
        
        /// <summary>
        /// Add recoil to a specific player (e.g., when they shoot)
        /// </summary>
        public void AddPlayerRecoil(ulong clientId, Vector3 positionRecoil, Vector3 rotationRecoil)
        {
            FirstPersonController player = GetPlayer(clientId);
            if (player != null)
            {
                ProceduralAnimations animations = player.GetComponent<ProceduralAnimations>();
                if (animations != null)
                {
                    animations.AddRecoil(positionRecoil, rotationRecoil);
                }
            }
        }
        
        #endregion

        private void OnDrawGizmosSelected()
        {
            // Draw spawn points
            if (spawnPoints != null)
            {
                Gizmos.color = Color.green;
                foreach (Transform spawnPoint in spawnPoints)
                {
                    if (spawnPoint != null)
                    {
                        Gizmos.DrawWireSphere(spawnPoint.position, 1f);
                        Gizmos.DrawRay(spawnPoint.position, spawnPoint.forward * 2f);
                    }
                }
            }
        }
    }
}
