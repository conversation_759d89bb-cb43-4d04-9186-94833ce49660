using System.Collections.Generic;
using Core;
using Core.Modules;
using Systems.ItemSystem;

namespace TestFix
{
    /// <summary>
    /// Simple test to verify that the record fix works correctly.
    /// This tests the specific scenario that was failing: using 'with' expression on Affix records.
    /// </summary>
    public class RecordFixTest
    {
        public static void TestAffixWithExpression()
        {
            // Setup test data
            Data.Affixes = new List<Affix>
            {
                new Affix(1, AffixType.Prefix, "Sharp", 1, new[] { "+{0} to Damage" }, new[] { 10.0f }, new[] { "Damage" }, 100),
                new Affix(2, AffixType.Suffix, "of Power", 2, new[] { "+{0}% Attack Speed" }, new[] { 15.0f }, new[] { "Speed" }, 80)
            };

            // Test the original failing scenario
            var originalAffix = Data.Affixes[0];
            var newTier = 5;
            
            // This should now work without compilation errors
            var modifiedAffix = originalAffix with { Tier = newTier };
            
            // Verify the modification worked
            System.Console.WriteLine($"Original tier: {originalAffix.Tier}");
            System.Console.WriteLine($"Modified tier: {modifiedAffix.Tier}");
            System.Console.WriteLine($"Other properties preserved: {modifiedAffix.Name == originalAffix.Name}");
            
            // Test FixedAffix functionality
            var item = new Item();
            var fixedAffix = new FixedAffix(1, 3);
            fixedAffix.Apply(item);
            
            var appliedAffix = item.Modules.OfType<Affix>().First();
            System.Console.WriteLine($"FixedAffix applied with tier: {appliedAffix.Tier}");
            
            System.Console.WriteLine("All tests passed! The record fix is working correctly.");
        }
    }
}
