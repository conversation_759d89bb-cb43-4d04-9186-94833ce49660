using UnityEngine;
using UnityEngine.InputSystem;
using PurrNet;
using PurrNet.Modules;

namespace FPS
{
    /// <summary>
    /// Professional First Person Controller based on Unity's CharacterController
    /// Follows the same patterns as Unity's Starter Assets and NeoFPS
    /// </summary>
    [RequireComponent(typeof(CharacterController))]
    [RequireComponent(typeof(PlayerInput))]
    public class FirstPersonController : NetworkBehaviour
    {
        [<PERSON><PERSON>("Player")]
        [<PERSON>lt<PERSON>("Move speed of the character in m/s")]
        public float MoveSpeed = 2.0f;
        
        [Tooltip("Sprint speed of the character in m/s")]
        public float SprintSpeed = 5.335f;
        
        [Tooltip("How fast the character turns to face movement direction")]
        [Range(0.0f, 0.3f)]
        public float RotationSmoothTime = 0.12f;
        
        [<PERSON>lt<PERSON>("Acceleration and deceleration")]
        public float SpeedChangeRate = 10.0f;

        [Space(10)]
        [Tooltip("The height the player can jump")]
        public float JumpHeight = 1.2f;
        
        [Toolt<PERSON>("The character uses its own gravity value. The engine default is -9.81f")]
        public float Gravity = -15.0f;

        [Space(10)]
        [Tooltip("Time required to pass before being able to jump again. Set to 0f to instantly jump again")]
        public float JumpTimeout = 0.50f;
        
        [Tooltip("Time required to pass before entering the fall state. Useful for walking down stairs")]
        public float FallTimeout = 0.15f;

        [Header("Player Grounded")]
        [Tooltip("If the character is grounded or not. Not part of the CharacterController built in grounded check")]
        public bool Grounded = true;
        
        [Tooltip("Useful for rough ground")]
        public float GroundedOffset = -0.14f;
        
        [Tooltip("The radius of the grounded check. Should match the radius of the CharacterController")]
        public float GroundedRadius = 0.28f;
        
        [Tooltip("What layers the character uses as ground")]
        public LayerMask GroundLayers = 1;

        [Header("Camera")]
        [Tooltip("The camera target that will be rotated for mouse look")]
        public GameObject CinemachineCameraTarget;

        [Tooltip("Mouse sensitivity multiplier")]
        public float MouseSensitivity = 1.0f;

        [Tooltip("Invert Y axis for mouse look")]
        public bool InvertY = false;

        [Tooltip("How far in degrees can you move the camera up")]
        public float TopClamp = 70.0f;

        [Tooltip("How far in degrees can you move the camera down")]
        public float BottomClamp = -30.0f;

        [Tooltip("Additional degress to override the camera. Useful for fine tuning camera position when locked")]
        public float CameraAngleOverride = 0.0f;

        [Tooltip("For locking the camera position on all axis")]
        public bool LockCameraPosition = false;

        [Header("Crouching")]
        [Tooltip("Enable crouching functionality")]
        public bool CanCrouch = true;

        [Tooltip("Height when crouching")]
        public float CrouchHeight = 1.0f;

        [Tooltip("Speed when crouching")]
        public float CrouchSpeed = 1.5f;

        [Tooltip("How fast to transition between standing and crouching")]
        public float CrouchTransitionSpeed = 8.0f;

        [Header("Networking")]
        [SerializeField] private SyncInput<Vector2> _moveInput = new();
        [SerializeField] private SyncInput<Vector2> _lookInput = new();
        [SerializeField] private SyncInput<bool> _jumpInput = new();
        [SerializeField] private SyncInput<bool> _sprintInput = new();
        [SerializeField] private SyncInput<bool> _crouchInput = new();

        // cinemachine
        private float _cinemachineTargetYaw;
        private float _cinemachineTargetPitch;

        // player
        private float _speed;
        private float _animationBlend;
        private float _targetRotation = 0.0f;
        private float _rotationVelocity;
        private float _verticalVelocity;
        private float _terminalVelocity = 53.0f;

        // timeout deltatime
        private float _jumpTimeoutDelta;
        private float _fallTimeoutDelta;

        // animation IDs
        private int _animIDSpeed;
        private int _animIDGrounded;
        private int _animIDJump;
        private int _animIDFreeFall;
        private int _animIDMotionSpeed;

        private PlayerInput _playerInput;
        private Animator _animator;
        private CharacterController _controller;
        private InputSystem_Actions _input;
        private GameObject _mainCamera;

        private const float _threshold = 0.01f;

        private bool _hasAnimator;

        // Input values
        private Vector2 _move;
        private Vector2 _look;
        private bool _jump;
        private bool _sprint;
        private bool _crouch;

        // Crouching
        private bool _isCrouching;
        private float _standingHeight;
        private float _currentHeight;
        private float _targetHeight;

        protected override void OnSpawned(bool asServer)
        {
            base.OnSpawned(asServer);
            
            // Get references
            _hasAnimator = TryGetComponent(out _animator);
            _controller = GetComponent<CharacterController>();
            _playerInput = GetComponent<PlayerInput>();
            
            // Only owner handles input
            if (!isOwner)
            {
                _playerInput.enabled = false;
                return;
            }

            _input = new InputSystem_Actions();
            _input.Player.Enable();

            // Get main camera
            if (_mainCamera == null)
            {
                _mainCamera = GameObject.FindGameObjectWithTag("MainCamera");
            }

            AssignAnimationIDs();

            // Reset timeouts on start
            _jumpTimeoutDelta = JumpTimeout;
            _fallTimeoutDelta = FallTimeout;

            // Initialize crouching
            _standingHeight = _controller.height;
            _currentHeight = _standingHeight;
            _targetHeight = _standingHeight;

            // Subscribe to PurrNet input events (server side)
            if (isServer)
            {
                _moveInput.onChanged += OnMoveInputChanged;
                _lookInput.onChanged += OnLookInputChanged;
                _jumpInput.onChanged += OnJumpInputChanged;
                _sprintInput.onChanged += OnSprintInputChanged;
                _crouchInput.onChanged += OnCrouchInputChanged;
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            if (isServer)
            {
                _moveInput.onChanged -= OnMoveInputChanged;
                _lookInput.onChanged -= OnLookInputChanged;
                _jumpInput.onChanged -= OnJumpInputChanged;
                _sprintInput.onChanged -= OnSprintInputChanged;
                _crouchInput.onChanged -= OnCrouchInputChanged;
            }

            if (isOwner && _input != null)
            {
                _input.Player.Disable();
                _input.Dispose();
            }
        }

        private void Update()
        {
            if (!isOwner)
                return;

            _hasAnimator = TryGetComponent(out _animator);

            // Handle input
            HandleInput();
            
            // Send input to server
            SendInputToServer();
        }

        private void LateUpdate()
        {
            if (!isOwner)
                return;
                
            CameraRotation();
        }

        private void FixedUpdate()
        {
            if (!isServer)
                return;

            GroundedCheck();
            JumpAndGravity();
            Move();
            HandleCrouching();
        }

        private void AssignAnimationIDs()
        {
            _animIDSpeed = Animator.StringToHash("Speed");
            _animIDGrounded = Animator.StringToHash("Grounded");
            _animIDJump = Animator.StringToHash("Jump");
            _animIDFreeFall = Animator.StringToHash("FreeFall");
            _animIDMotionSpeed = Animator.StringToHash("MotionSpeed");
        }

        private void HandleInput()
        {
            _move = _input.Player.Move.ReadValue<Vector2>();
            _look = _input.Player.Look.ReadValue<Vector2>();
            _jump = _input.Player.Jump.triggered;
            _sprint = _input.Player.Sprint.IsPressed();
            _crouch = _input.Player.Crouch.IsPressed();
        }

        private void SendInputToServer()
        {
            _moveInput.value = _move;
            _lookInput.value = _look * MouseSensitivity;
            _jumpInput.value = _jump;
            _sprintInput.value = _sprint;
            _crouchInput.value = _crouch;
        }

        private void GroundedCheck()
        {
            // Set sphere position, with offset
            Vector3 spherePosition = new Vector3(transform.position.x, transform.position.y - GroundedOffset,
                transform.position.z);
            Grounded = Physics.CheckSphere(spherePosition, GroundedRadius, GroundLayers,
                QueryTriggerInteraction.Ignore);

            // Update animator if using character
            if (_hasAnimator)
            {
                _animator.SetBool(_animIDGrounded, Grounded);
            }
        }

        private void CameraRotation()
        {
            // If there is an input and camera position is not fixed
            if (_look.sqrMagnitude >= _threshold && !LockCameraPosition)
            {
                // Apply mouse sensitivity and invert Y if needed
                float lookX = _look.x;
                float lookY = InvertY ? _look.y : -_look.y;

                _cinemachineTargetYaw += lookX;
                _cinemachineTargetPitch += lookY;
            }

            // Clamp our rotations so our values are limited 360 degrees
            _cinemachineTargetYaw = ClampAngle(_cinemachineTargetYaw, float.MinValue, float.MaxValue);
            _cinemachineTargetPitch = ClampAngle(_cinemachineTargetPitch, BottomClamp, TopClamp);

            // Apply rotation to camera target
            CinemachineCameraTarget.transform.rotation = Quaternion.Euler(_cinemachineTargetPitch + CameraAngleOverride,
                _cinemachineTargetYaw, 0.0f);
        }

        private void Move()
        {
            // Set target speed based on move speed, sprint speed, crouch and if sprint is pressed
            float targetSpeed = MoveSpeed;

            if (_isCrouching)
            {
                targetSpeed = CrouchSpeed;
            }
            else if (_sprint)
            {
                targetSpeed = SprintSpeed;
            }

            // A simplistic acceleration and deceleration designed to be easy to remove, replace, or iterate upon
            if (_move == Vector2.zero) targetSpeed = 0.0f;

            // A reference to the players current horizontal velocity
            float currentHorizontalSpeed = new Vector3(_controller.velocity.x, 0.0f, _controller.velocity.z).magnitude;

            float speedOffset = 0.1f;
            float inputMagnitude = _move.magnitude;

            // Accelerate or decelerate to target speed
            if (currentHorizontalSpeed < targetSpeed - speedOffset ||
                currentHorizontalSpeed > targetSpeed + speedOffset)
            {
                // Creates curved result rather than a linear one giving a more organic speed change
                _speed = Mathf.Lerp(currentHorizontalSpeed, targetSpeed * inputMagnitude,
                    Time.deltaTime * SpeedChangeRate);

                // Round speed to 3 decimal places
                _speed = Mathf.Round(_speed * 1000f) / 1000f;
            }
            else
            {
                _speed = targetSpeed;
            }

            _animationBlend = Mathf.Lerp(_animationBlend, targetSpeed, Time.deltaTime * SpeedChangeRate);
            if (_animationBlend < 0.01f) _animationBlend = 0f;

            // Calculate movement direction relative to camera (FPS style)
            Vector3 inputDirection = new Vector3(_move.x, 0.0f, _move.y);

            // Get camera forward and right vectors (flatten Y component for ground movement)
            Vector3 cameraForward = CinemachineCameraTarget.transform.forward;
            Vector3 cameraRight = CinemachineCameraTarget.transform.right;

            // Remove Y component for ground movement
            cameraForward.y = 0f;
            cameraRight.y = 0f;
            cameraForward.Normalize();
            cameraRight.Normalize();

            // Calculate movement direction relative to camera
            Vector3 targetDirection = (cameraForward * inputDirection.z + cameraRight * inputDirection.x).normalized;

            // Move the player
            _controller.Move(targetDirection.normalized * (_speed * Time.deltaTime) +
                             new Vector3(0.0f, _verticalVelocity, 0.0f) * Time.deltaTime);

            // Update animator if using character
            if (_hasAnimator)
            {
                _animator.SetFloat(_animIDSpeed, _animationBlend);
                _animator.SetFloat(_animIDMotionSpeed, inputMagnitude);
            }
        }

        private void JumpAndGravity()
        {
            if (Grounded)
            {
                // Reset the fall timeout timer
                _fallTimeoutDelta = FallTimeout;

                // Update animator if using character
                if (_hasAnimator)
                {
                    _animator.SetBool(_animIDJump, false);
                    _animator.SetBool(_animIDFreeFall, false);
                }

                // Stop our velocity dropping infinitely when grounded
                if (_verticalVelocity < 0.0f)
                {
                    _verticalVelocity = -2f;
                }

                // Jump (can't jump while crouching)
                if (_jump && _jumpTimeoutDelta <= 0.0f && !_isCrouching)
                {
                    // The square root of H * -2 * G = how much velocity needed to reach desired height
                    _verticalVelocity = Mathf.Sqrt(JumpHeight * -2f * Gravity);

                    // Update animator if using character
                    if (_hasAnimator)
                    {
                        _animator.SetBool(_animIDJump, true);
                    }
                }

                // Jump timeout
                if (_jumpTimeoutDelta >= 0.0f)
                {
                    _jumpTimeoutDelta -= Time.deltaTime;
                }
            }
            else
            {
                // Reset the jump timeout timer
                _jumpTimeoutDelta = JumpTimeout;

                // Fall timeout
                if (_fallTimeoutDelta >= 0.0f)
                {
                    _fallTimeoutDelta -= Time.deltaTime;
                }
                else
                {
                    // Update animator if using character
                    if (_hasAnimator)
                    {
                        _animator.SetBool(_animIDFreeFall, true);
                    }
                }
            }

            // Apply gravity over time if under terminal (multiply by delta time twice to linearly speed up over time)
            if (_verticalVelocity < _terminalVelocity)
            {
                _verticalVelocity += Gravity * Time.deltaTime;
            }
        }

        private void HandleCrouching()
        {
            if (!CanCrouch) return;

            // Update crouch state
            _isCrouching = _crouch;

            // Set target height
            _targetHeight = _isCrouching ? CrouchHeight : _standingHeight;

            // Smooth height transition
            if (Mathf.Abs(_currentHeight - _targetHeight) > 0.01f)
            {
                _currentHeight = Mathf.Lerp(_currentHeight, _targetHeight, CrouchTransitionSpeed * Time.fixedDeltaTime);

                // Update character controller
                _controller.height = _currentHeight;

                // Adjust center to keep feet on ground
                Vector3 center = _controller.center;
                center.y = _currentHeight * 0.5f;
                _controller.center = center;
            }
        }

        private static float ClampAngle(float lfAngle, float lfMin, float lfMax)
        {
            if (lfAngle < -360f) lfAngle += 360f;
            if (lfAngle > 360f) lfAngle -= 360f;
            return Mathf.Clamp(lfAngle, lfMin, lfMax);
        }

        private void OnDrawGizmosSelected()
        {
            Color transparentGreen = new Color(0.0f, 1.0f, 0.0f, 0.35f);
            Color transparentRed = new Color(1.0f, 0.0f, 0.0f, 0.35f);

            if (Grounded) Gizmos.color = transparentGreen;
            else Gizmos.color = transparentRed;

            // When selected, draw a gizmo in the position of, and matching radius of, the grounded collider
            Gizmos.DrawSphere(
                new Vector3(transform.position.x, transform.position.y - GroundedOffset, transform.position.z),
                GroundedRadius);
        }

        #region PurrNet Input Handlers

        private void OnMoveInputChanged(Vector2 input)
        {
            _move = input;
        }

        private void OnLookInputChanged(Vector2 input)
        {
            _look = input;
        }

        private void OnJumpInputChanged(bool input)
        {
            _jump = input;
        }

        private void OnSprintInputChanged(bool input)
        {
            _sprint = input;
        }

        private void OnCrouchInputChanged(bool input)
        {
            _crouch = input;
        }

        #endregion

        #region Public Properties

        public bool IsGrounded => Grounded;
        public bool IsCrouching => _isCrouching;
        public bool IsSprinting => _sprint;
        public Vector3 Velocity => _controller.velocity;
        public float CurrentSpeed => _speed;

        #endregion
    }
}
