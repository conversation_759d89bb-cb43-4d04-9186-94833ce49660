%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &132815821595183813
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3191005264102906804}
  - component: {fileID: 3779597635912502767}
  - component: {fileID: 4254404607923969629}
  m_Layer: 0
  m_Name: Capsule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3191005264102906804
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 132815821595183813}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8616695264987045621}
  m_Father: {fileID: 4115861454220257477}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3779597635912502767
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 132815821595183813}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4254404607923969629
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 132815821595183813}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2380891297824680867
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8616695264987045621}
  - component: {fileID: 4875510063895020791}
  - component: {fileID: 269522495211855804}
  m_Layer: 0
  m_Name: Capsule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8616695264987045621
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2380891297824680867}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: 0.7071065, w: 0.7071072}
  m_LocalPosition: {x: 0, y: 0.5, z: 0.375}
  m_LocalScale: {x: 0.3, y: 0.3, z: 0.3}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3191005264102906804}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 90}
--- !u!33 &4875510063895020791
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2380891297824680867}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &269522495211855804
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2380891297824680867}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 451033b5f63c74b4fa30e88caa9fb3b2, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &3791465309707741931
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4115861454220257477}
  - component: {fileID: 3218946167149650902}
  - component: {fileID: 4860593028732995557}
  - component: {fileID: 7161648320398111876}
  - component: {fileID: 4735627248964533641}
  - component: {fileID: 172035305287916526}
  - component: {fileID: 6892571115014006305}
  - component: {fileID: 4068555599563872585}
  - component: {fileID: 1804635173533761807}
  - component: {fileID: 582231814072870042}
  m_Layer: 0
  m_Name: Player
  m_TagString: Player
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4115861454220257477
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3791465309707741931}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1344988657059920650}
  - {fileID: 3191005264102906804}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3218946167149650902
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3791465309707741931}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 817fe5016bd0a2a49a95b6eb338bb132, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _isSetup: 0
  _prefabId: -2147483648
  _componentIndex: -2147483648
  _shouldBePooled: 0
  _parent: {fileID: 0}
  _invertedPathToNearestParent: 
  _directChildren: []
  _networkRules: {fileID: 0}
  _visitiblityRules: {fileID: 0}
  _syncPosition: 1
  _syncRotation: 1
  _syncScale: 1
  _syncParent: 1
  _interpolateSettings: 7
  _minBufferSize: 1
  _maxBufferSize: 2
  _characterControllerPatch: 0
  _ownerAuth: 0
  _interpolationTiming: 0
--- !u!114 &4860593028732995557
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3791465309707741931}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39d5e4b97b1615f4aae984a3d5f4235f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _isSetup: 0
  _prefabId: -2147483648
  _componentIndex: -2147483648
  _shouldBePooled: 0
  _parent: {fileID: 0}
  _invertedPathToNearestParent: 
  _directChildren: []
  _networkRules: {fileID: 0}
  _visitiblityRules: {fileID: 0}
  _components: []
  _gameObjects: []
  _toActivate: []
  _toDeactivate: []
  _toEnable: []
  _toDisable: []
--- !u!114 &7161648320398111876
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3791465309707741931}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c97173f1f7d2bc145949c79c2463725d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _isSetup: 0
  _prefabId: -2147483648
  _componentIndex: -2147483648
  _shouldBePooled: 0
  _parent: {fileID: 0}
  _invertedPathToNearestParent: 
  _directChildren: []
  _networkRules: {fileID: 0}
  _visitiblityRules: {fileID: 0}
  mouseSensitivity: 0.01
  invertYAxis: 0
  movementInput:
    _value:
      movement: {x: 0, y: 0}
    _simulatedHostPing: 0
  lookInput:
    _value:
      lookDelta: {x: 0, y: 0}
    _simulatedHostPing: 0
  actionInput:
    _value:
      jump: 0
      crouch: 0
      sprint: 0
      attack: 0
      interact: 0
    _simulatedHostPing: 0
--- !u!54 &4735627248964533641
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3791465309707741931}
  serializedVersion: 5
  m_Mass: 1
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &172035305287916526
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3791465309707741931}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5
  m_Height: 1
  m_Direction: 1
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &6892571115014006305
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3791465309707741931}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 89680e29096d9964c9bd6d8959d3ef7f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _isSetup: 0
  _prefabId: -2147483648
  _componentIndex: -2147483648
  _shouldBePooled: 0
  _parent: {fileID: 0}
  _invertedPathToNearestParent: 
  _directChildren: []
  _networkRules: {fileID: 0}
  _visitiblityRules: {fileID: 0}
  walkSpeed: 5
  runSpeed: 8
  crouchSpeed: 2.5
  jumpForce: 8
  airControl: 0.3
  groundCheckDistance: 0.1
  groundLayerMask:
    serializedVersion: 2
    m_Bits: 1
  maxSlopeAngle: 45
  standingHeight: 2
  crouchHeight: 1
  crouchTransitionSpeed: 8
--- !u!114 &4068555599563872585
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3791465309707741931}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e2961c51efbbea64a904dd54fc6b7bc3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _isSetup: 0
  _prefabId: -2147483648
  _componentIndex: -2147483648
  _shouldBePooled: 0
  _parent: {fileID: 0}
  _invertedPathToNearestParent: 
  _directChildren: []
  _networkRules: {fileID: 0}
  _visitiblityRules: {fileID: 0}
  playerCamera: {fileID: 7878547760046379579}
  cameraRoot: {fileID: 1344988657059920650}
  mouseSensitivity: 0.01
  invertYAxis: 0
  minVerticalAngle: -90
  maxVerticalAngle: 90
  enableSmoothing: 0
  smoothingFactor: 5
--- !u!114 &1804635173533761807
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3791465309707741931}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 11f96a8590e24ea42a63d336a0db5308, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _isSetup: 0
  _prefabId: -2147483648
  _componentIndex: -2147483648
  _shouldBePooled: 0
  _parent: {fileID: 0}
  _invertedPathToNearestParent: 
  _directChildren: []
  _networkRules: {fileID: 0}
  _visitiblityRules: {fileID: 0}
  enableHeadBob: 1
  bobFrequency: 2
  bobHorizontalAmplitude: 0.1
  bobVerticalAmplitude: 0.05
  bobCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  enableWeaponSway: 1
  swayAmount: 0.02
  swayMaxAmount: 0.06
  swaySmooth: 6
  swayResetSmooth: 2
  enableBreathing: 1
  breathingFrequency: 0.5
  breathingAmplitude: 0.01
  breathingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  enableCameraShake: 1
  shakeDecayRate: 5
  enableLandingAnimation: 1
  landingDuration: 0.3
  landingIntensity: 0.1
  landingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  enableRecoil: 1
  recoilDecayRate: 8
  recoilReturnSpeed: 4
--- !u!114 &582231814072870042
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3791465309707741931}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5ac4fa238bcd6da40b3c64e72cceef1a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _isSetup: 0
  _prefabId: -2147483648
  _componentIndex: -2147483648
  _shouldBePooled: 0
  _parent: {fileID: 0}
  _invertedPathToNearestParent: 
  _directChildren: []
  _networkRules: {fileID: 0}
  _visitiblityRules: {fileID: 0}
  enableOnStart: 1
  debugMode: 1
  inputHandler: {fileID: 7161648320398111876}
  movementController: {fileID: 6892571115014006305}
  cameraController: {fileID: 4068555599563872585}
  animationSystem: {fileID: 1804635173533761807}
--- !u!1 &6255911693103415486
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1344988657059920650}
  m_Layer: 0
  m_Name: CameraRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1344988657059920650
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6255911693103415486}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.6, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2239434723689133430}
  m_Father: {fileID: 4115861454220257477}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9198077339357206890
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2239434723689133430}
  - component: {fileID: 7878547760046379579}
  - component: {fileID: 4051028535086245705}
  - component: {fileID: 1908011492399174791}
  m_Layer: 0
  m_Name: Camera
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2239434723689133430
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9198077339357206890}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1344988657059920650}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!20 &7878547760046379579
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9198077339357206890}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 1
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.1
  far clip plane: 1000
  field of view: 80.579216
  orthographic: 0
  orthographic size: 5
  m_Depth: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!81 &4051028535086245705
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9198077339357206890}
  m_Enabled: 1
--- !u!114 &1908011492399174791
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9198077339357206890}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a79441f348de89743a2939f4d699eac1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_RenderShadows: 1
  m_RequiresDepthTextureOption: 2
  m_RequiresOpaqueTextureOption: 2
  m_CameraType: 0
  m_Cameras: []
  m_RendererIndex: -1
  m_VolumeLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_VolumeTrigger: {fileID: 0}
  m_VolumeFrameworkUpdateModeOption: 2
  m_RenderPostProcessing: 0
  m_Antialiasing: 0
  m_AntialiasingQuality: 2
  m_StopNaN: 0
  m_Dithering: 0
  m_ClearDepth: 1
  m_AllowXRRendering: 1
  m_AllowHDROutput: 1
  m_UseScreenCoordOverride: 0
  m_ScreenSizeOverride: {x: 0, y: 0, z: 0, w: 0}
  m_ScreenCoordScaleBias: {x: 0, y: 0, z: 0, w: 0}
  m_RequiresDepthTexture: 0
  m_RequiresColorTexture: 0
  m_Version: 2
  m_TaaSettings:
    m_Quality: 3
    m_FrameInfluence: 0.1
    m_JitterScale: 1
    m_MipBias: 0
    m_VarianceClampScale: 0.9
    m_ContrastAdaptiveSharpening: 0
