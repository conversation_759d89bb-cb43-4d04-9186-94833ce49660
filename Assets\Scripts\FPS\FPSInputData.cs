using System;
using UnityEngine;

namespace FPS
{
    /// <summary>
    /// Input data structure for FPS movement (WASD)
    /// Compatible with PurrNet's SyncInput system
    /// </summary>
    [Serializable]
    public struct FPSMovementInput : IEquatable<FPSMovementInput>
    {
        public Vector2 movement;
        
        public FPSMovementInput(Vector2 movement)
        {
            this.movement = movement;
        }
        
        public bool Equals(FPSMovementInput other)
        {
            return movement.Equals(other.movement);
        }
        
        public override bool Equals(object obj)
        {
            return obj is FPSMovementInput other && Equals(other);
        }
        
        public override int GetHashCode()
        {
            return movement.GetHashCode();
        }
        
        public static bool operator ==(FPSMovementInput left, FPSMovementInput right)
        {
            return left.Equals(right);
        }
        
        public static bool operator !=(FPSMovementInput left, FPSMovementInput right)
        {
            return !left.Equals(right);
        }
    }
    
    /// <summary>
    /// Input data structure for FPS camera look (mouse movement)
    /// Compatible with PurrNet's SyncInput system
    /// </summary>
    [Serializable]
    public struct FPSLookInput : IEquatable<FPSLookInput>
    {
        public Vector2 lookDelta;
        
        public FPSLookInput(Vector2 lookDelta)
        {
            this.lookDelta = lookDelta;
        }
        
        public bool Equals(FPSLookInput other)
        {
            return lookDelta.Equals(other.lookDelta);
        }
        
        public override bool Equals(object obj)
        {
            return obj is FPSLookInput other && Equals(other);
        }
        
        public override int GetHashCode()
        {
            return lookDelta.GetHashCode();
        }
        
        public static bool operator ==(FPSLookInput left, FPSLookInput right)
        {
            return left.Equals(right);
        }
        
        public static bool operator !=(FPSLookInput left, FPSLookInput right)
        {
            return !left.Equals(right);
        }
    }
    
    /// <summary>
    /// Input data structure for FPS actions (jump, crouch, sprint, etc.)
    /// Compatible with PurrNet's SyncInput system
    /// </summary>
    [Serializable]
    public struct FPSActionInput : IEquatable<FPSActionInput>
    {
        public bool jump;
        public bool crouch;
        public bool sprint;
        public bool attack;
        public bool interact;
        
        public FPSActionInput(bool jump = false, bool crouch = false, bool sprint = false, bool attack = false, bool interact = false)
        {
            this.jump = jump;
            this.crouch = crouch;
            this.sprint = sprint;
            this.attack = attack;
            this.interact = interact;
        }
        
        public bool Equals(FPSActionInput other)
        {
            return jump == other.jump && 
                   crouch == other.crouch && 
                   sprint == other.sprint && 
                   attack == other.attack && 
                   interact == other.interact;
        }
        
        public override bool Equals(object obj)
        {
            return obj is FPSActionInput other && Equals(other);
        }
        
        public override int GetHashCode()
        {
            return HashCode.Combine(jump, crouch, sprint, attack, interact);
        }
        
        public static bool operator ==(FPSActionInput left, FPSActionInput right)
        {
            return left.Equals(right);
        }
        
        public static bool operator !=(FPSActionInput left, FPSActionInput right)
        {
            return !left.Equals(right);
        }
    }
    
    /// <summary>
    /// Combined input data structure for all FPS inputs
    /// This can be used for a single SyncInput if preferred over separate ones
    /// </summary>
    [Serializable]
    public struct FPSCombinedInput : IEquatable<FPSCombinedInput>
    {
        public FPSMovementInput movement;
        public FPSLookInput look;
        public FPSActionInput actions;
        
        public FPSCombinedInput(FPSMovementInput movement, FPSLookInput look, FPSActionInput actions)
        {
            this.movement = movement;
            this.look = look;
            this.actions = actions;
        }
        
        public bool Equals(FPSCombinedInput other)
        {
            return movement.Equals(other.movement) && 
                   look.Equals(other.look) && 
                   actions.Equals(other.actions);
        }
        
        public override bool Equals(object obj)
        {
            return obj is FPSCombinedInput other && Equals(other);
        }
        
        public override int GetHashCode()
        {
            return HashCode.Combine(movement, look, actions);
        }
        
        public static bool operator ==(FPSCombinedInput left, FPSCombinedInput right)
        {
            return left.Equals(right);
        }
        
        public static bool operator !=(FPSCombinedInput left, FPSCombinedInput right)
        {
            return !left.Equals(right);
        }
    }
}
