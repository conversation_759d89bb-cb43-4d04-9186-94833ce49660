using UnityEngine;
using UnityEngine.InputSystem;
using PurrNet;
using PurrNet.Modules;

namespace FPS
{
    /// <summary>
    /// FPS Input Handler that integrates Unity's New Input System with PurrNet's SyncInput
    /// Handles all input for FPS controller including movement, look, and actions
    /// </summary>
    public class FPSInputHandler : NetworkBehaviour
    {
        [Header("Input Settings")]
        [SerializeField] private float mouseSensitivity = 2f;
        [SerializeField] private bool invertYAxis = false;
        
        [Header("PurrNet SyncInput")]
        [SerializeField] private SyncInput<FPSMovementInput> movementInput = new();
        [SerializeField] private SyncInput<FPSLookInput> lookInput = new();
        [SerializeField] private SyncInput<FPSActionInput> actionInput = new();
        
        // Input System references
        private InputSystem_Actions inputActions;
        private InputAction moveAction;
        private InputAction lookAction;
        private InputAction jumpAction;
        private InputAction crouchAction;
        private InputAction sprintAction;
        private InputAction attackAction;
        private InputAction interactAction;
        
        // Current input states
        private Vector2 currentMovement;
        private Vector2 currentLookDelta;
        private bool currentJump;
        private bool currentCrouch;
        private bool currentSprint;
        private bool currentAttack;
        private bool currentInteract;
        
        // Events for other components to subscribe to
        public System.Action<FPSMovementInput> OnMovementChanged;
        public System.Action<FPSLookInput> OnLookChanged;
        public System.Action<FPSActionInput> OnActionChanged;
        
        protected override void OnSpawned(bool asServer)
        {
            base.OnSpawned(asServer);
            
            // Only the owner handles input
            if (!isOwner)
                return;
                
            // Initialize Input System
            InitializeInputSystem();
            
            // Subscribe to SyncInput events (server receives these)
            if (isServer)
            {
                movementInput.onChanged += HandleMovementInputChanged;
                lookInput.onChanged += HandleLookInputChanged;
                actionInput.onChanged += HandleActionInputChanged;
            }
        }
        
        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            // Cleanup Input System
            CleanupInputSystem();
            
            // Unsubscribe from SyncInput events
            if (isServer)
            {
                movementInput.onChanged -= HandleMovementInputChanged;
                lookInput.onChanged -= HandleLookInputChanged;
                actionInput.onChanged -= HandleActionInputChanged;
            }
        }
        
        private void InitializeInputSystem()
        {
            inputActions = new InputSystem_Actions();
            
            // Get input actions
            moveAction = inputActions.Player.Move;
            lookAction = inputActions.Player.Look;
            jumpAction = inputActions.Player.Jump;
            crouchAction = inputActions.Player.Crouch;
            sprintAction = inputActions.Player.Sprint;
            attackAction = inputActions.Player.Attack;
            interactAction = inputActions.Player.Interact;
            
            // Subscribe to input events
            moveAction.performed += OnMovePerformed;
            moveAction.canceled += OnMoveCanceled;
            
            lookAction.performed += OnLookPerformed;
            
            jumpAction.performed += OnJumpPerformed;
            jumpAction.canceled += OnJumpCanceled;
            
            crouchAction.performed += OnCrouchPerformed;
            crouchAction.canceled += OnCrouchCanceled;
            
            sprintAction.performed += OnSprintPerformed;
            sprintAction.canceled += OnSprintCanceled;
            
            attackAction.performed += OnAttackPerformed;
            attackAction.canceled += OnAttackCanceled;
            
            interactAction.performed += OnInteractPerformed;
            interactAction.canceled += OnInteractCanceled;
            
            // Enable input
            inputActions.Enable();
        }
        
        private void CleanupInputSystem()
        {
            if (inputActions != null)
            {
                inputActions.Disable();
                inputActions.Dispose();
            }
        }
        
        private void Update()
        {
            if (!isOwner)
                return;
                
            // Send input to server via SyncInput
            SendInputToServer();
        }
        
        private void SendInputToServer()
        {
            // Create input structures
            var movement = new FPSMovementInput(currentMovement);
            var look = new FPSLookInput(currentLookDelta * mouseSensitivity * (invertYAxis ? new Vector2(1, -1) : Vector2.one));
            var actions = new FPSActionInput(currentJump, currentCrouch, currentSprint, currentAttack, currentInteract);
            
            // Send via SyncInput (automatically filters unchanged values)
            movementInput.value = movement;
            lookInput.value = look;
            actionInput.value = actions;
            
            // Reset look delta after sending (mouse delta should only be applied once)
            currentLookDelta = Vector2.zero;
        }
        
        #region Input System Event Handlers
        
        private void OnMovePerformed(InputAction.CallbackContext context)
        {
            currentMovement = context.ReadValue<Vector2>();
        }
        
        private void OnMoveCanceled(InputAction.CallbackContext context)
        {
            currentMovement = Vector2.zero;
        }
        
        private void OnLookPerformed(InputAction.CallbackContext context)
        {
            currentLookDelta = context.ReadValue<Vector2>();
        }
        
        private void OnJumpPerformed(InputAction.CallbackContext context)
        {
            currentJump = true;
        }
        
        private void OnJumpCanceled(InputAction.CallbackContext context)
        {
            currentJump = false;
        }
        
        private void OnCrouchPerformed(InputAction.CallbackContext context)
        {
            currentCrouch = true;
        }
        
        private void OnCrouchCanceled(InputAction.CallbackContext context)
        {
            currentCrouch = false;
        }
        
        private void OnSprintPerformed(InputAction.CallbackContext context)
        {
            currentSprint = true;
        }
        
        private void OnSprintCanceled(InputAction.CallbackContext context)
        {
            currentSprint = false;
        }
        
        private void OnAttackPerformed(InputAction.CallbackContext context)
        {
            currentAttack = true;
        }
        
        private void OnAttackCanceled(InputAction.CallbackContext context)
        {
            currentAttack = false;
        }
        
        private void OnInteractPerformed(InputAction.CallbackContext context)
        {
            currentInteract = true;
        }
        
        private void OnInteractCanceled(InputAction.CallbackContext context)
        {
            currentInteract = false;
        }
        
        #endregion
        
        #region SyncInput Event Handlers (Server Side)
        
        private void HandleMovementInputChanged(FPSMovementInput input)
        {
            OnMovementChanged?.Invoke(input);
        }
        
        private void HandleLookInputChanged(FPSLookInput input)
        {
            OnLookChanged?.Invoke(input);
        }
        
        private void HandleActionInputChanged(FPSActionInput input)
        {
            OnActionChanged?.Invoke(input);
        }
        
        #endregion
        
        #region Public Properties
        
        public float MouseSensitivity
        {
            get => mouseSensitivity;
            set => mouseSensitivity = value;
        }
        
        public bool InvertYAxis
        {
            get => invertYAxis;
            set => invertYAxis = value;
        }
        
        #endregion
    }
}
