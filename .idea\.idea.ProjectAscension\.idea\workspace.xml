<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8aa3c0aa-0374-404b-8e4c-5faaeb0f59f1" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/.idea.ProjectAscension/.idea/AugmentWebviewStateStore.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/.idea.ProjectAscension/.idea/AugmentWebviewStateStore.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/.idea.ProjectAscension/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/.idea.ProjectAscension/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/InputSystem_Actions.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/InputSystem_Actions.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/InputSystem_Actions.inputactions" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/InputSystem_Actions.inputactions" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Prefabs/Main Camera.prefab" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Prefabs/Main Camera.prefab.meta" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Prefabs/Player.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Prefabs/Player.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Scenes/SampleScene.unity" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Scenes/SampleScene.unity" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Core/Entities/Chest.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Core/Entities/Item.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="mock://C:/Projects/Unity/ProjectAscension/Assets/Scripts/Core/Modules/AffixModules.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Projects/Unity/ProjectAscension/Assets/Scripts/Core/Modules/AffixModules.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Projects/Unity/ProjectAscension/Assets/Scripts/Core/Modules/AffixModules.cs.meta" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Projects/Unity/ProjectAscension/Assets/Scripts/Systems/UnifiedModularSystemDemo.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Projects/Unity/ProjectAscension/Assets/StreamingAssets/Affixes.json" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.ugui@57cef44123c7/Runtime/TMP/TMP_Dropdown.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/dev.purrnet.purrnet@8f6a33cca935/Runtime/Components/NetworkIdentity/NetworkIdentity.cs" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2zbLkGxsVzk0SmsVyLW4wNrElVT" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Attach to Unity Editor.Attach to Unity Editor.executor&quot;: &quot;Debug&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Attach to Unity Editor.Attach to Unity Editor">
    <configuration name="Start Unity" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-projectPath C:\Projects\Unity\ProjectAscension -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="C:\Projects\Unity\ProjectAscension" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <method v="2" />
    </configuration>
    <configuration name="Unit Tests (batch mode)" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-runTests -batchmode -projectPath C:\Projects\Unity\ProjectAscension -testResults Logs/results.xml -logFile Logs/Editor.log -testPlatform EditMode -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="C:\Projects\Unity\ProjectAscension" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <method v="2" />
    </configuration>
    <configuration name="Attach to Unity Editor &amp; Play" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="UNITY_ATTACH_AND_PLAY" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="runtimes">
        <list />
      </option>
      <option name="selectedOptions">
        <list />
      </option>
      <option name="useMixedMode" value="false" />
      <method v="2" />
    </configuration>
    <configuration name="Attach to Unity Editor" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="Unity Debug" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="runtimes">
        <list />
      </option>
      <option name="selectedOptions">
        <list />
      </option>
      <option name="useMixedMode" value="false" />
      <method v="2" />
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="8aa3c0aa-0374-404b-8e4c-5faaeb0f59f1" name="Changes" comment="" />
      <created>1751994967643</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751994967643</updated>
      <workItem from="1751994970208" duration="4147000" />
      <workItem from="1752089797700" duration="704000" />
      <workItem from="1752133174381" duration="10658000" />
      <workItem from="1752255573699" duration="61000" />
      <workItem from="1752255705206" duration="3954000" />
      <workItem from="1752408735465" duration="1891000" />
      <workItem from="1752505374458" duration="24000" />
      <workItem from="1752505409760" duration="1616000" />
      <workItem from="1752681578560" duration="720000" />
      <workItem from="1752747653138" duration="844000" />
      <workItem from="1752748552955" duration="506000" />
      <workItem from="1752749090728" duration="17189000" />
      <workItem from="1752953713580" duration="2365000" />
      <workItem from="1753033886179" duration="4000" />
      <workItem from="1753085969130" duration="977000" />
      <workItem from="1753355506286" duration="1278000" />
      <workItem from="1753356840277" duration="1052000" />
      <workItem from="1753357931302" duration="9308000" />
      <workItem from="1753376918763" duration="739000" />
      <workItem from="1753388094447" duration="373000" />
      <workItem from="1753418571599" duration="57000" />
      <workItem from="1753418833680" duration="7125000" />
      <workItem from="1753734218244" duration="1258000" />
      <workItem from="1753797652589" duration="435000" />
      <workItem from="1753800001976" duration="110000" />
      <workItem from="1753885924791" duration="4601000" />
      <workItem from="1754744865266" duration="27000" />
      <workItem from="1754744948614" duration="894000" />
    </task>
    <task id="LOCAL-00001" summary=".">
      <option name="closed" value="true" />
      <created>1752410157310</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752410157310</updated>
    </task>
    <task id="LOCAL-00002" summary=".">
      <option name="closed" value="true" />
      <created>1752505383783</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752505383783</updated>
    </task>
    <task id="LOCAL-00003" summary=".">
      <option name="closed" value="true" />
      <created>1753355698269</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753355698269</updated>
    </task>
    <task id="LOCAL-00004" summary=".">
      <option name="closed" value="true" />
      <created>1753375240823</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753375240823</updated>
    </task>
    <task id="LOCAL-00005" summary=".">
      <option name="closed" value="true" />
      <created>1753419184897</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753419184897</updated>
    </task>
    <task id="LOCAL-00006" summary=".">
      <option name="closed" value="true" />
      <created>1753419293759</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753419293759</updated>
    </task>
    <task id="LOCAL-00007" summary=".">
      <option name="closed" value="true" />
      <created>1753419887035</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753419887035</updated>
    </task>
    <task id="LOCAL-00008" summary=".">
      <option name="closed" value="true" />
      <created>1753420792865</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753420792865</updated>
    </task>
    <task id="LOCAL-00009" summary=".">
      <option name="closed" value="true" />
      <created>1753426256077</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753426256077</updated>
    </task>
    <task id="LOCAL-00010" summary=".">
      <option name="closed" value="true" />
      <created>1753885957521</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753885957521</updated>
    </task>
    <task id="LOCAL-00011" summary=".">
      <option name="closed" value="true" />
      <created>1753894614221</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1753894614221</updated>
    </task>
    <task id="LOCAL-00012" summary=".">
      <option name="closed" value="true" />
      <created>1753896626924</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1753896626924</updated>
    </task>
    <task id="LOCAL-00013" summary=".">
      <option name="closed" value="true" />
      <created>1753901131811</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1753901131811</updated>
    </task>
    <option name="localTasksCounter" value="14" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="true" />
  <component name="UnityProjectDiscoverer">
    <option name="hasUnityReference" value="true" />
    <option name="unityProject" value="true" />
    <option name="unityProjectFolder" value="true" />
  </component>
  <component name="UnityUnitTestConfiguration" currentTestLauncher="Both" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
    <MESSAGE value="." />
    <option name="LAST_COMMIT_MESSAGE" value="." />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>