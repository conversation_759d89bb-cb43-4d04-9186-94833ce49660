using UnityEngine;
using PurrNet;

namespace FPS
{
    /// <summary>
    /// Helper script to automatically setup FPS controller in test scenes
    /// Attach this to an empty GameObject and it will create a complete FPS setup
    /// </summary>
    public class FPSTestSceneSetup : MonoBehaviour
    {
        [Header("Setup Options")]
        [SerializeField] private bool autoSetupOnStart = true;
        [SerializeField] private bool createTestEnvironment = true;
        [SerializeField] private Vector3 spawnPosition = new Vector3(0, 2, 0);
        
        [Header("FPS Controller Settings")]
        [SerializeField] private float mouseSensitivity = 2f;
        [SerializeField] private bool invertYAxis = false;
        [SerializeField] private bool enableDebugMode = true;
        
        [Header("Test Environment")]
        [SerializeField] private Material groundMaterial;
        [SerializeField] private Material wallMaterial;
        
        private void Start()
        {
            if (autoSetupOnStart)
            {
                SetupFPSController();
                
                if (createTestEnvironment)
                {
                    CreateTestEnvironment();
                }
            }
        }
        
        [ContextMenu("Setup FPS Controller")]
        public void SetupFPSController()
        {
            // Create main FPS player GameObject
            GameObject fpsPlayer = new GameObject("FPS_Player");
            fpsPlayer.transform.position = spawnPosition;
            
            // Add required components
            SetupPhysicsComponents(fpsPlayer);
            SetupNetworkComponents(fpsPlayer);
            SetupFPSComponents(fpsPlayer);
            SetupCameraHierarchy(fpsPlayer);
            
            Debug.Log("FPS Controller setup complete!");
        }
        
        private void SetupPhysicsComponents(GameObject player)
        {
            // Add Rigidbody
            Rigidbody rb = player.AddComponent<Rigidbody>();
            rb.mass = 1f;
            rb.linearDamping = 0f;
            rb.angularDamping = 0f;
            rb.useGravity = true;
            rb.isKinematic = false;
            rb.interpolation = RigidbodyInterpolation.Interpolate;
            rb.collisionDetectionMode = CollisionDetectionMode.Continuous;
            
            // Add CapsuleCollider
            CapsuleCollider capsule = player.AddComponent<CapsuleCollider>();
            capsule.center = new Vector3(0, 1, 0);
            capsule.radius = 0.5f;
            capsule.height = 2f;
            capsule.isTrigger = false;
        }
        
        private void SetupNetworkComponents(GameObject player)
        {
            // Add NetworkIdentity (PurrNet)
            NetworkIdentity networkIdentity = player.AddComponent<NetworkIdentity>();
            // Note: You'll need to configure this in the NetworkManager's prefabs list
            
            Debug.Log("Network components added. Remember to add this prefab to your NetworkPrefabs ScriptableObject!");
        }
        
        private void SetupFPSComponents(GameObject player)
        {
            // Add all FPS controller components
            FPSInputHandler inputHandler = player.AddComponent<FPSInputHandler>();
            FPSMovementController movementController = player.AddComponent<FPSMovementController>();
            FPSCameraController cameraController = player.AddComponent<FPSCameraController>();
            ProceduralAnimationSystem animationSystem = player.AddComponent<ProceduralAnimationSystem>();
            FPSController fpsController = player.AddComponent<FPSController>();
            
            // Configure settings
            inputHandler.MouseSensitivity = mouseSensitivity;
            inputHandler.InvertYAxis = invertYAxis;
            
            // Enable debug mode if requested
            if (enableDebugMode)
            {
                // Use reflection to set private field (for testing purposes)
                var debugField = typeof(FPSController).GetField("debugMode", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                debugField?.SetValue(fpsController, true);
            }
        }
        
        private void SetupCameraHierarchy(GameObject player)
        {
            // Create camera root
            GameObject cameraRoot = new GameObject("Camera_Root");
            cameraRoot.transform.SetParent(player.transform);
            cameraRoot.transform.localPosition = new Vector3(0, 1.6f, 0);
            cameraRoot.transform.localRotation = Quaternion.identity;
            
            // Create main camera
            GameObject cameraObj = new GameObject("Main Camera");
            cameraObj.transform.SetParent(cameraRoot.transform);
            cameraObj.transform.localPosition = Vector3.zero;
            cameraObj.transform.localRotation = Quaternion.identity;
            
            // Add Camera component
            Camera camera = cameraObj.AddComponent<Camera>();
            camera.fieldOfView = 75f;
            camera.nearClipPlane = 0.1f;
            camera.farClipPlane = 1000f;
            
            // Add AudioListener
            cameraObj.AddComponent<AudioListener>();
            
            // Tag as MainCamera
            cameraObj.tag = "MainCamera";
            
            // Configure FPSCameraController reference
            FPSCameraController cameraController = player.GetComponent<FPSCameraController>();
            if (cameraController != null)
            {
                // Use reflection to set private fields (for testing purposes)
                var cameraField = typeof(FPSCameraController).GetField("playerCamera", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                cameraField?.SetValue(cameraController, camera);
                
                var rootField = typeof(FPSCameraController).GetField("cameraRoot", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                rootField?.SetValue(cameraController, cameraRoot.transform);
            }
        }
        
        [ContextMenu("Create Test Environment")]
        public void CreateTestEnvironment()
        {
            CreateGround();
            CreateWalls();
            CreateTestObjects();
            
            Debug.Log("Test environment created!");
        }
        
        private void CreateGround()
        {
            GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
            ground.name = "Ground";
            ground.transform.position = Vector3.zero;
            ground.transform.localScale = new Vector3(10, 1, 10);
            
            if (groundMaterial != null)
            {
                ground.GetComponent<Renderer>().material = groundMaterial;
            }
            else
            {
                // Create a simple gray material
                Material mat = new Material(Shader.Find("Standard"));
                mat.color = Color.gray;
                ground.GetComponent<Renderer>().material = mat;
            }
        }
        
        private void CreateWalls()
        {
            // Create 4 walls around the test area
            Vector3[] wallPositions = {
                new Vector3(0, 2.5f, 50),   // North
                new Vector3(0, 2.5f, -50),  // South
                new Vector3(50, 2.5f, 0),   // East
                new Vector3(-50, 2.5f, 0)   // West
            };
            
            Vector3[] wallRotations = {
                Vector3.zero,               // North
                Vector3.zero,               // South
                new Vector3(0, 90, 0),      // East
                new Vector3(0, 90, 0)       // West
            };
            
            for (int i = 0; i < wallPositions.Length; i++)
            {
                GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
                wall.name = $"Wall_{i}";
                wall.transform.position = wallPositions[i];
                wall.transform.rotation = Quaternion.Euler(wallRotations[i]);
                wall.transform.localScale = new Vector3(100, 5, 1);
                
                if (wallMaterial != null)
                {
                    wall.GetComponent<Renderer>().material = wallMaterial;
                }
                else
                {
                    // Create a simple brown material
                    Material mat = new Material(Shader.Find("Standard"));
                    mat.color = new Color(0.6f, 0.4f, 0.2f);
                    wall.GetComponent<Renderer>().material = mat;
                }
            }
        }
        
        private void CreateTestObjects()
        {
            // Create some test objects for interaction
            CreateTestCube(new Vector3(5, 1, 5), Color.red);
            CreateTestCube(new Vector3(-5, 1, 5), Color.blue);
            CreateTestCube(new Vector3(5, 1, -5), Color.green);
            CreateTestCube(new Vector3(-5, 1, -5), Color.yellow);
            
            // Create a ramp for testing
            GameObject ramp = GameObject.CreatePrimitive(PrimitiveType.Cube);
            ramp.name = "Test_Ramp";
            ramp.transform.position = new Vector3(10, 1, 0);
            ramp.transform.rotation = Quaternion.Euler(0, 0, 15);
            ramp.transform.localScale = new Vector3(2, 0.1f, 8);
            
            Material rampMat = new Material(Shader.Find("Standard"));
            rampMat.color = Color.cyan;
            ramp.GetComponent<Renderer>().material = rampMat;
        }
        
        private void CreateTestCube(Vector3 position, Color color)
        {
            GameObject cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
            cube.name = "Test_Cube";
            cube.transform.position = position;
            cube.transform.localScale = Vector3.one * 2f;
            
            Material mat = new Material(Shader.Find("Standard"));
            mat.color = color;
            cube.GetComponent<Renderer>().material = mat;
            
            // Add Rigidbody for physics interaction
            Rigidbody rb = cube.AddComponent<Rigidbody>();
            rb.mass = 1f;
        }
        
        private void OnGUI()
        {
            if (!enableDebugMode)
                return;
                
            GUILayout.BeginArea(new Rect(Screen.width - 220, 10, 200, 150));
            GUILayout.Label("FPS Test Scene Setup", GUI.skin.box);
            
            if (GUILayout.Button("Setup FPS Controller"))
            {
                SetupFPSController();
            }
            
            if (GUILayout.Button("Create Test Environment"))
            {
                CreateTestEnvironment();
            }
            
            if (GUILayout.Button("Clear Scene"))
            {
                ClearTestScene();
            }
            
            GUILayout.EndArea();
        }
        
        private void ClearTestScene()
        {
            // Find and destroy test objects
            GameObject[] testObjects = GameObject.FindGameObjectsWithTag("Untagged");
            foreach (GameObject obj in testObjects)
            {
                if (obj.name.StartsWith("FPS_") || obj.name.StartsWith("Test_") || 
                    obj.name == "Ground" || obj.name.StartsWith("Wall_"))
                {
                    DestroyImmediate(obj);
                }
            }
            
            Debug.Log("Test scene cleared!");
        }
    }
}
