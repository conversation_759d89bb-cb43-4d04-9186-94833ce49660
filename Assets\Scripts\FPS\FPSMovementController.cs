using UnityEngine;
using PurrNet;

namespace FPS
{
    /// <summary>
    /// FPS Movement Controller with physics-based movement
    /// <PERSON><PERSON> walking, running, jumping, crouching, and ground detection
    /// </summary>
    [RequireComponent(typeof(Rigidbody), typeof(CapsuleCollider))]
    public class FPSMovementController : NetworkBehaviour
    {
        [Header("Movement Settings")]
        [SerializeField] private float walkSpeed = 5f;
        [SerializeField] private float runSpeed = 8f;
        [SerializeField] private float crouchSpeed = 2.5f;
        [SerializeField] private float jumpForce = 8f;
        [SerializeField] private float airControl = 0.3f;
        
        [Header("Physics Settings")]
        [SerializeField] private float groundCheckDistance = 0.1f;
        [SerializeField] private LayerMask groundLayerMask = 1;
        [SerializeField] private float maxSlopeAngle = 45f;
        
        [Header("Crouch Settings")]
        [SerializeField] private float standingHeight = 2f;
        [SerializeField] private float crouchHeight = 1f;
        [SerializeField] private float crouchTransitionSpeed = 8f;
        
        // Components
        private Rigidbody rb;
        private CapsuleCollider capsuleCollider;
        private FPSInputHandler inputHandler;
        
        // Movement state
        private Vector3 moveDirection;
        private bool isGrounded;
        private bool isCrouching;
        private bool isRunning;
        private float currentSpeed;
        private float targetHeight;
        private float currentHeight;
        
        // Ground detection
        private RaycastHit groundHit;
        private Vector3 groundNormal;
        
        // Events for procedural animations
        public System.Action<Vector3> OnMovementChanged;
        public System.Action<bool> OnGroundedChanged;
        public System.Action<bool> OnCrouchChanged;
        public System.Action<bool> OnRunningChanged;
        public System.Action OnJumped;
        public System.Action OnLanded;
        
        protected override void OnSpawned(bool asServer)
        {
            base.OnSpawned(asServer);
            
            // Get components
            rb = GetComponent<Rigidbody>();
            capsuleCollider = GetComponent<CapsuleCollider>();
            inputHandler = GetComponent<FPSInputHandler>();
            
            // Configure rigidbody
            rb.freezeRotation = true;
            rb.interpolation = RigidbodyInterpolation.Interpolate;
            
            // Only server handles physics
            if (!isServer)
            {
                rb.isKinematic = true;
                return;
            }
            
            // Subscribe to input events
            if (inputHandler != null)
            {
                inputHandler.OnMovementChanged += HandleMovementInput;
                inputHandler.OnActionChanged += HandleActionInput;
            }
            
            // Initialize state
            targetHeight = standingHeight;
            currentHeight = standingHeight;
            capsuleCollider.height = standingHeight;
        }
        
        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            // Unsubscribe from input events
            if (inputHandler != null)
            {
                inputHandler.OnMovementChanged -= HandleMovementInput;
                inputHandler.OnActionChanged -= HandleActionInput;
            }
        }
        
        private void FixedUpdate()
        {
            if (!isServer)
                return;
                
            // Check ground
            CheckGrounded();
            
            // Handle movement
            HandleMovement();
            
            // Handle crouching
            HandleCrouching();
        }
        
        private void CheckGrounded()
        {
            bool wasGrounded = isGrounded;
            
            // Raycast down from center of capsule
            Vector3 rayStart = transform.position + Vector3.up * (capsuleCollider.radius + 0.1f);
            isGrounded = Physics.Raycast(rayStart, Vector3.down, out groundHit, 
                capsuleCollider.radius + groundCheckDistance, groundLayerMask);
            
            if (isGrounded)
            {
                groundNormal = groundHit.normal;
                
                // Check slope angle
                float slopeAngle = Vector3.Angle(groundNormal, Vector3.up);
                if (slopeAngle > maxSlopeAngle)
                {
                    isGrounded = false;
                }
            }
            
            // Trigger events
            if (wasGrounded != isGrounded)
            {
                OnGroundedChanged?.Invoke(isGrounded);
                
                if (isGrounded && !wasGrounded)
                {
                    OnLanded?.Invoke();
                }
            }
        }
        
        private void HandleMovement()
        {
            // Calculate movement direction relative to transform
            Vector3 forward = transform.forward;
            Vector3 right = transform.right;
            
            // Remove Y component for ground movement
            forward.y = 0;
            right.y = 0;
            forward.Normalize();
            right.Normalize();
            
            // Calculate desired movement
            Vector3 desiredMove = (forward * moveDirection.z + right * moveDirection.x).normalized;
            
            // Apply speed
            desiredMove *= currentSpeed;
            
            // Apply movement
            if (isGrounded)
            {
                // Ground movement
                Vector3 velocity = rb.linearVelocity;
                velocity.x = desiredMove.x;
                velocity.z = desiredMove.z;
                rb.linearVelocity = velocity;
            }
            else
            {
                // Air control
                Vector3 airForce = desiredMove * airControl;
                rb.AddForce(airForce, ForceMode.Acceleration);
            }
            
            // Trigger movement event for animations
            OnMovementChanged?.Invoke(new Vector3(rb.linearVelocity.x, 0, rb.linearVelocity.z));
        }
        
        private void HandleCrouching()
        {
            // Smooth height transition
            if (Mathf.Abs(currentHeight - targetHeight) > 0.01f)
            {
                currentHeight = Mathf.Lerp(currentHeight, targetHeight, 
                    crouchTransitionSpeed * Time.fixedDeltaTime);
                
                // Update collider
                capsuleCollider.height = currentHeight;
                
                // Adjust center to keep feet on ground
                Vector3 center = capsuleCollider.center;
                center.y = currentHeight * 0.5f;
                capsuleCollider.center = center;
            }
        }
        
        private void HandleMovementInput(FPSMovementInput input)
        {
            moveDirection = new Vector3(input.movement.x, 0, input.movement.y);
            
            // Calculate current speed based on state
            if (isCrouching)
            {
                currentSpeed = crouchSpeed;
            }
            else if (isRunning && moveDirection.magnitude > 0.1f)
            {
                currentSpeed = runSpeed;
            }
            else
            {
                currentSpeed = walkSpeed;
            }
        }
        
        private void HandleActionInput(FPSActionInput input)
        {
            // Handle jumping
            if (input.jump && isGrounded && !isCrouching)
            {
                Jump();
            }
            
            // Handle crouching
            bool wasCrouching = isCrouching;
            isCrouching = input.crouch;
            
            if (isCrouching != wasCrouching)
            {
                targetHeight = isCrouching ? crouchHeight : standingHeight;
                OnCrouchChanged?.Invoke(isCrouching);
            }
            
            // Handle running
            bool wasRunning = isRunning;
            isRunning = input.sprint && !isCrouching;
            
            if (isRunning != wasRunning)
            {
                OnRunningChanged?.Invoke(isRunning);
            }
        }
        
        private void Jump()
        {
            // Apply jump force
            Vector3 velocity = rb.linearVelocity;
            velocity.y = jumpForce;
            rb.linearVelocity = velocity;
            
            // Trigger jump event
            OnJumped?.Invoke();
        }
        
        #region Public Properties
        
        public bool IsGrounded => isGrounded;
        public bool IsCrouching => isCrouching;
        public bool IsRunning => isRunning;
        public Vector3 Velocity => rb.linearVelocity;
        public float CurrentSpeed => currentSpeed;
        public Vector3 MoveDirection => moveDirection;
        
        #endregion
        
        #region Gizmos
        
        private void OnDrawGizmosSelected()
        {
            if (capsuleCollider == null)
                return;
                
            // Draw ground check ray
            Gizmos.color = isGrounded ? Color.green : Color.red;
            Vector3 rayStart = transform.position + Vector3.up * (capsuleCollider.radius + 0.1f);
            Gizmos.DrawRay(rayStart, Vector3.down * (capsuleCollider.radius + groundCheckDistance));
            
            // Draw ground normal
            if (isGrounded)
            {
                Gizmos.color = Color.blue;
                Gizmos.DrawRay(groundHit.point, groundNormal * 2f);
            }
        }
        
        #endregion
    }
}
