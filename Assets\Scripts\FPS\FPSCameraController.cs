using UnityEngine;
using PurrNet;

namespace FPS
{
    /// <summary>
    /// FPS Camera Controller with competitive FPS mouse look
    /// <PERSON>les camera rotation and integrates with procedural animation system
    /// Separate from movement controller for clean architecture
    /// </summary>
    public class FPSCameraController : NetworkBehaviour
    {
        [Header("Camera Settings")]
        [SerializeField] private Camera playerCamera;
        [SerializeField] private Transform cameraRoot;
        [SerializeField] private float mouseSensitivity = 2f;
        [SerializeField] private bool invertYAxis = false;
        
        [Header("Look Constraints")]
        [SerializeField] private float minVerticalAngle = -90f;
        [SerializeField] private float maxVerticalAngle = 90f;
        
        [Header("Smoothing")]
        [SerializeField] private bool enableSmoothing = false;
        [SerializeField] private float smoothingFactor = 5f;
        
        // Components
        private FPSInputHandler inputHandler;
        private ProceduralAnimationSystem animationSystem;
        
        // Rotation state
        private float verticalRotation = 0f;
        private float horizontalRotation = 0f;
        private Vector2 targetRotation;
        private Vector2 currentRotation;
        
        // Original camera position for procedural animations
        private Vector3 originalCameraPosition;
        private Vector3 originalCameraRotation;
        
        protected override void OnSpawned(bool asServer)
        {
            base.OnSpawned(asServer);
            
            // Only the owner controls the camera
            if (!isOwner)
            {
                // Disable camera for non-owners
                if (playerCamera != null)
                    playerCamera.enabled = false;
                return;
            }
            
            // Get components
            inputHandler = GetComponent<FPSInputHandler>();
            animationSystem = GetComponent<ProceduralAnimationSystem>();
            
            // Setup camera
            SetupCamera();
            
            // Subscribe to input events
            if (inputHandler != null)
            {
                inputHandler.OnLookChanged += HandleLookInput;
            }
            
            // Store original camera transform
            if (cameraRoot != null)
            {
                originalCameraPosition = cameraRoot.localPosition;
                originalCameraRotation = cameraRoot.localEulerAngles;
            }
            else if (playerCamera != null)
            {
                originalCameraPosition = playerCamera.transform.localPosition;
                originalCameraRotation = playerCamera.transform.localEulerAngles;
            }
        }
        
        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            // Unsubscribe from input events
            if (inputHandler != null)
            {
                inputHandler.OnLookChanged -= HandleLookInput;
            }
        }
        
        private void SetupCamera()
        {
            // Ensure we have a camera
            if (playerCamera == null)
            {
                playerCamera = GetComponentInChildren<Camera>();
                if (playerCamera == null)
                {
                    Debug.LogError("FPSCameraController: No camera found!");
                    return;
                }
            }
            
            // Setup camera root if not assigned
            if (cameraRoot == null)
            {
                cameraRoot = playerCamera.transform;
            }
            
            // Lock cursor for FPS gameplay
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
            
            // Initialize rotation from current transform
            Vector3 currentEuler = transform.eulerAngles;
            horizontalRotation = currentEuler.y;
            verticalRotation = cameraRoot.localEulerAngles.x;
            
            // Handle angle wrapping for vertical rotation
            if (verticalRotation > 180f)
                verticalRotation -= 360f;
        }
        
        private void LateUpdate()
        {
            if (!isOwner)
                return;
                
            // Apply camera rotation
            ApplyCameraRotation();
            
            // Apply procedural animations
            ApplyProceduralAnimations();
        }
        
        private void ApplyCameraRotation()
        {
            if (enableSmoothing)
            {
                // Smooth rotation
                currentRotation = Vector2.Lerp(currentRotation, targetRotation, 
                    smoothingFactor * Time.deltaTime);
                
                verticalRotation = currentRotation.x;
                horizontalRotation = currentRotation.y;
            }
            
            // Clamp vertical rotation
            verticalRotation = Mathf.Clamp(verticalRotation, minVerticalAngle, maxVerticalAngle);
            
            // Apply rotations
            // Horizontal rotation (Y-axis) applied to the player body
            transform.rotation = Quaternion.Euler(0f, horizontalRotation, 0f);
            
            // Vertical rotation (X-axis) applied to the camera
            cameraRoot.localRotation = Quaternion.Euler(verticalRotation, 0f, 0f);
        }
        
        private void ApplyProceduralAnimations()
        {
            if (animationSystem == null)
                return;
                
            // Get animation offsets from procedural animation system
            Vector3 positionOffset = animationSystem.GetCameraPositionOffset();
            Vector3 rotationOffset = animationSystem.GetCameraRotationOffset();
            
            // Apply position offset
            Vector3 targetPosition = originalCameraPosition + positionOffset;
            cameraRoot.localPosition = targetPosition;
            
            // Apply rotation offset (additive to look rotation)
            Vector3 currentEuler = cameraRoot.localEulerAngles;
            Vector3 targetEuler = new Vector3(
                verticalRotation + rotationOffset.x,
                rotationOffset.y,
                rotationOffset.z
            );
            cameraRoot.localRotation = Quaternion.Euler(targetEuler);
        }
        
        private void HandleLookInput(FPSLookInput input)
        {
            // Apply mouse sensitivity and invert Y if needed
            Vector2 lookDelta = input.lookDelta;
            
            if (invertYAxis)
                lookDelta.y = -lookDelta.y;
            
            // Update rotation values
            if (enableSmoothing)
            {
                // Set target for smooth rotation
                targetRotation.x -= lookDelta.y;
                targetRotation.y += lookDelta.x;
                targetRotation.x = Mathf.Clamp(targetRotation.x, minVerticalAngle, maxVerticalAngle);
            }
            else
            {
                // Direct rotation for competitive feel
                verticalRotation -= lookDelta.y;
                horizontalRotation += lookDelta.x;
            }
        }
        
        #region Public Methods
        
        /// <summary>
        /// Reset camera to original position and rotation
        /// </summary>
        public void ResetCamera()
        {
            if (cameraRoot != null)
            {
                cameraRoot.localPosition = originalCameraPosition;
                cameraRoot.localEulerAngles = originalCameraRotation;
            }
        }
        
        /// <summary>
        /// Add recoil to camera rotation
        /// </summary>
        public void AddRecoil(Vector2 recoilAmount)
        {
            verticalRotation -= recoilAmount.y;
            horizontalRotation += recoilAmount.x;
        }
        
        /// <summary>
        /// Set mouse sensitivity
        /// </summary>
        public void SetMouseSensitivity(float sensitivity)
        {
            mouseSensitivity = sensitivity;
            if (inputHandler != null)
            {
                inputHandler.MouseSensitivity = sensitivity;
            }
        }
        
        /// <summary>
        /// Toggle Y-axis inversion
        /// </summary>
        public void SetInvertYAxis(bool invert)
        {
            invertYAxis = invert;
            if (inputHandler != null)
            {
                inputHandler.InvertYAxis = invert;
            }
        }
        
        #endregion
        
        #region Public Properties
        
        public Camera PlayerCamera => playerCamera;
        public Transform CameraRoot => cameraRoot;
        public float VerticalRotation => verticalRotation;
        public float HorizontalRotation => horizontalRotation;
        public Vector3 OriginalCameraPosition => originalCameraPosition;
        public Vector3 OriginalCameraRotation => originalCameraRotation;
        
        #endregion
        
        #region Gizmos
        
        private void OnDrawGizmosSelected()
        {
            if (cameraRoot == null)
                return;
                
            // Draw camera look direction
            Gizmos.color = Color.blue;
            Gizmos.DrawRay(cameraRoot.position, cameraRoot.forward * 5f);
            
            // Draw vertical rotation limits
            Gizmos.color = Color.yellow;
            Vector3 minDir = Quaternion.Euler(minVerticalAngle, transform.eulerAngles.y, 0) * Vector3.forward;
            Vector3 maxDir = Quaternion.Euler(maxVerticalAngle, transform.eulerAngles.y, 0) * Vector3.forward;
            Gizmos.DrawRay(cameraRoot.position, minDir * 3f);
            Gizmos.DrawRay(cameraRoot.position, maxDir * 3f);
        }
        
        #endregion
    }
}
