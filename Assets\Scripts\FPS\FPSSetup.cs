using UnityEngine;
using UnityEngine.InputSystem;
using PurrNet;

namespace FPS
{
    /// <summary>
    /// Simple setup script for the FPS controller
    /// Creates a proper FPS setup based on Unity's Starter Assets approach
    /// </summary>
    public class FPSSetup : MonoBehaviour
    {
        [Header("Setup")]
        [SerializeField] private bool setupOnStart = true;
        [SerializeField] private Vector3 spawnPosition = new Vector3(0, 2, 0);
        
        [Header("Camera")]
        [SerializeField] private float mouseSensitivity = 1f;
        [SerializeField] private bool invertY = false;

        private void Start()
        {
            if (setupOnStart)
            {
                CreateFPSPlayer();
            }
        }

        [ContextMenu("Create FPS Player")]
        public void CreateFPSPlayer()
        {
            // Create main player object
            GameObject player = new GameObject("FPS_Player");
            player.transform.position = spawnPosition;
            player.tag = "Player";

            // Add CharacterController (the proper way)
            CharacterController controller = player.AddComponent<CharacterController>();
            controller.center = new Vector3(0, 1, 0);
            controller.radius = 0.5f;
            controller.height = 2f;
            controller.stepOffset = 0.3f;
            controller.slopeLimit = 45f;

            // Add PlayerInput
            PlayerInput playerInput = player.AddComponent<PlayerInput>();
            
            // Add NetworkIdentity for PurrNet
            NetworkIdentity networkIdentity = player.AddComponent<NetworkIdentity>();

            // Add our FPS controller
            FirstPersonController fpsController = player.AddComponent<FirstPersonController>();
            
            // Add procedural animations
            ProceduralAnimations animations = player.AddComponent<ProceduralAnimations>();

            // Create camera hierarchy
            CreateCameraHierarchy(player, fpsController);

            // Create simple test environment
            CreateTestEnvironment();

            Debug.Log("FPS Player created successfully!");
        }

        private void CreateCameraHierarchy(GameObject player, FirstPersonController controller)
        {
            // Create camera target (for Cinemachine-style setup)
            GameObject cameraTarget = new GameObject("CameraTarget");
            cameraTarget.transform.SetParent(player.transform);
            cameraTarget.transform.localPosition = new Vector3(0, 1.6f, 0);

            // Set the camera target in the controller
            controller.CinemachineCameraTarget = cameraTarget;

            // Create main camera
            GameObject cameraObj = new GameObject("PlayerCamera");
            cameraObj.transform.SetParent(cameraTarget.transform);
            cameraObj.transform.localPosition = Vector3.zero;
            cameraObj.tag = "MainCamera";

            // Add Camera component
            Camera camera = cameraObj.AddComponent<Camera>();
            camera.fieldOfView = 75f;
            camera.nearClipPlane = 0.1f;
            camera.farClipPlane = 1000f;

            // Add AudioListener
            cameraObj.AddComponent<AudioListener>();

            Debug.Log("Camera hierarchy created");
        }

        private void CreateTestEnvironment()
        {
            // Create ground
            GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
            ground.name = "Ground";
            ground.transform.position = Vector3.zero;
            ground.transform.localScale = new Vector3(10, 1, 10);

            // Create some walls
            CreateWall(new Vector3(0, 2.5f, 50), new Vector3(100, 5, 1), "North Wall");
            CreateWall(new Vector3(0, 2.5f, -50), new Vector3(100, 5, 1), "South Wall");
            CreateWall(new Vector3(50, 2.5f, 0), new Vector3(1, 5, 100), "East Wall");
            CreateWall(new Vector3(-50, 2.5f, 0), new Vector3(1, 5, 100), "West Wall");

            // Create some test objects
            CreateTestCube(new Vector3(5, 1, 5), Color.red);
            CreateTestCube(new Vector3(-5, 1, 5), Color.blue);
            CreateTestCube(new Vector3(5, 1, -5), Color.green);

            Debug.Log("Test environment created");
        }

        private void CreateWall(Vector3 position, Vector3 scale, string name)
        {
            GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
            wall.name = name;
            wall.transform.position = position;
            wall.transform.localScale = scale;

            // Simple material
            Material mat = new Material(Shader.Find("Standard"));
            mat.color = new Color(0.6f, 0.4f, 0.2f);
            wall.GetComponent<Renderer>().material = mat;
        }

        private void CreateTestCube(Vector3 position, Color color)
        {
            GameObject cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
            cube.name = "TestCube";
            cube.transform.position = position;
            cube.transform.localScale = Vector3.one * 2f;

            // Add physics
            Rigidbody rb = cube.AddComponent<Rigidbody>();
            rb.mass = 1f;

            // Color it
            Material mat = new Material(Shader.Find("Standard"));
            mat.color = color;
            cube.GetComponent<Renderer>().material = mat;
        }

        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 200, 100));
            
            if (GUILayout.Button("Create FPS Player"))
            {
                CreateFPSPlayer();
            }
            
            if (GUILayout.Button("Test Camera Shake"))
            {
                ProceduralAnimations anim = FindObjectOfType<ProceduralAnimations>();
                if (anim != null)
                {
                    anim.AddCameraShake(0.3f);
                }
            }
            
            if (GUILayout.Button("Test Recoil"))
            {
                ProceduralAnimations anim = FindObjectOfType<ProceduralAnimations>();
                if (anim != null)
                {
                    Vector3 posRecoil = new Vector3(0, 0, -0.1f);
                    Vector3 rotRecoil = new Vector3(-2f, Random.Range(-1f, 1f), 0);
                    anim.AddRecoil(posRecoil, rotRecoil);
                }
            }
            
            GUILayout.EndArea();
        }
    }
}
